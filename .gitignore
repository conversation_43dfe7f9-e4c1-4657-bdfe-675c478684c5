*.d
*.o
*.obj
*.out
*.dep
*.i
*.hex*
*.htm
*.crf
*.iex
*.csv
*.dep
*.lst
*.map
*.uvguix*
*.uvoptx
*.bin
JLinkLog.txt
JlinkSettings.ini
*.zip
*.asf
*.asm
*.scvd
*.axf
*.elf
*.lnp
*.yml
*.log

compile_commands.json
ArInp.Scr
_bld.txt
buildlog_*.txt
ER_IROM*
JUMP_TABLE
RTE_Components.h
*.lib
!/components/ethermind/lib/meshlibs/phyos/keil/*.lib

# ignor folder
#RTE/
bin/
components/driver_rom/
/lib/generate_mesh_lib/ethermind/build/*
!/lib/generate_mesh_lib/ethermind/build/*.defines
!/lib/generate_mesh_lib/ethermind/build/*.bat
!/lib/generate_mesh_lib/ethermind/build/*.sh
!/lib/generate_mesh_lib/ethermind/build/*.py
!/lib/generate_mesh_lib/ethermind/build/*.phy6200
!/lib/generate_mesh_lib/ethermind/build/*.keil
!/lib/generate_mesh_lib/ethermind/build/*.armgcc

#VS Coder
*.code-workspace
.project

#Force include file/folder
!example/OTA/OTA_internal_flash/bin/
!**/OTA/*/bin/ota*.hex

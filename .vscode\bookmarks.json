{"files": [{"path": "project/ble_mesh/mesh_light/source/bleMesh/appl_sample_example_phylight.c", "bookmarks": [{"line": 879, "column": 0, "label": ""}, {"line": 1357, "column": 18, "label": ""}]}, {"path": "components/ethermind/mesh/export/vendormodel/server/vendormodel_server.c", "bookmarks": [{"line": 116, "column": 11, "label": ""}]}, {"path": "components/ethermind/platforms/mesh/blebrr_pl.c", "bookmarks": [{"line": 807, "column": 21, "label": ""}]}, {"path": "components/ethermind/mesh/export/climodel/cli_model.c", "bookmarks": [{"line": 398, "column": 30, "label": ""}]}, {"path": "components/libraries/cli/cliface.c", "bookmarks": [{"line": 258, "column": 0, "label": ""}]}, {"path": "project/ble_mesh/mesh_light/source/bleMesh/bleMesh.c", "bookmarks": [{"line": 156, "column": 10, "label": ""}]}, {"path": "components/ethermind/mesh/export/bearer/blebrr.c", "bookmarks": [{"line": 998, "column": 21, "label": ""}]}]}
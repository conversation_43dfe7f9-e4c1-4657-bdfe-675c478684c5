{"configurations": [{"name": "Target 1", "cStandard": "c17", "cppStandard": "c++17", "intelliSenseMode": "${default}", "includePath": ["C:/Keil_v5/ARM/ARMCC/include", "C:/Keil_v5/ARM/ARMCC/include/rw", "components/inc", "components/ble/controller/include", "components/osal/include", "components/common", "components/ble/include", "components/ble/hci", "components/ble/host", "components/Profiles/DevInfo", "components/Profiles/SimpleProfile", "components/Profiles/Roles", "components/driver/common", "components/driver/clock", "components/driver/gpio", "components/driver/pwrmgr", "components/driver/log", "components/driver/uart", "components/driver/flash", "components/driver/pwm", "project/ble_mesh/mesh_light/source", "project/ble_mesh/mesh_light/source/bleMesh", "components/ethermind/external/crypto/aes", "components/ethermind/mesh/export/include", "components/ethermind/mesh/export/bearer", "components/ethermind/mesh/export/platforms/ext", "components/ethermind/osal/src/phyos", "components/ethermind/utils/include", "components/ethermind/mesh/export/appl", "components/osal/snv", "components/ethermind/external/crypto/asm_ecdh_p256", "components/ethermind/external/crypto/sha256", "components/ethermind/lib", "components/profiles/aliGenie", "components/ethermind/mesh/export/cbtimer", "components/profiles/ota_app", "components/arch/cm0", "misc", "components/driver/timer", "components/ble/controller", "components/libraries/crc16", "components/libraries/cli", "components/ethermind/mesh/export/climodel", "components/libraries/fs", "components/ethermind/mesh/export/vendormodel", "components/ethermind/mesh/export/vendormodel/server", "components/ethermind/platforms", "components/ethermind/platforms/interfaces/crypto", "components/ethermind/platforms/mesh", "components/driver/led_light", "components/libraries/circular_buf"], "defines": ["CFG_CP", "BLE_AT_ENABLE", "OSAL_CBTIMER_NUM_TASKS=1", "MTU_SIZE=247", "OSALMEM_METRICS=0", "DEBUG_INFO=1", "CFG_SLEEP_MODE=PWR_MODE_NO_SLEEP", "CFG_HEARTBEAT_MODE=0", "CFG_MESH_FAST_PRO=0", "PHY_MCU_TYPE=MCU_BUMBEE_M0", "USE_FS=0", "MAX_NUM_LL_CONN=1", "GATT_MAX_NUM_CONN=MAX_NUM_LL_CONN+1", "MESH_HEAP=0", "_RTE_", "__CC_ARM", "__arm__", "__align(x)=", "__ALIGNOF__(x)=", "__alignof__(x)=", "__asm(x)=", "__forceinline=", "__restrict=", "__global_reg(n)=", "__inline=", "__int64=long long", "__INTADDR__(expr)=0", "__irq=", "__packed=", "__pure=", "__smc(n)=", "__svc(n)=", "__svc_indirect(n)=", "__svc_indirect_r7(n)=", "__value_in_regs=", "__weak=", "__writeonly=", "__declspec(x)=", "__attribute__(x)=", "__nonnull__(x)=", "__register=", "__breakpoint(x)=", "__cdp(x,y,z)=", "__clrex()=", "__clz(x)=0U", "__current_pc()=0U", "__current_sp()=0U", "__disable_fiq()=", "__disable_irq()=", "__dmb(x)=", "__dsb(x)=", "__enable_fiq()=", "__enable_irq()=", "__fabs(x)=0.0", "__fabsf(x)=0.0f", "__force_loads()=", "__force_stores()=", "__isb(x)=", "__ldrex(x)=0U", "__ldrexd(x)=0U", "__ldrt(x)=0U", "__memory_changed()=", "__nop()=", "__pld(...)=", "__pli(...)=", "__qadd(x,y)=0", "__qdbl(x)=0", "__qsub(x,y)=0", "__rbit(x)=0U", "__rev(x)=0U", "__return_address()=0U", "__ror(x,y)=0U", "__schedule_barrier()=", "__semihost(x,y)=0", "__sev()=", "__sqrt(x)=0.0", "__sqrtf(x)=0.0f", "__ssat(x,y)=0", "__strex(x,y)=0U", "__strexd(x,y)=0", "__strt(x,y)=", "__swp(x,y)=0U", "__usat(x,y)=0U", "__wfe()=", "__wfi()=", "__yield()=", "__vfp_status(x,y)=0"]}], "version": 4}
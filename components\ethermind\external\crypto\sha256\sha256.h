/**
    \file sha256.h

    \brief This file contains SHA-224 and SHA-256 definitions and functions.

    The Secure Hash Algorithms 224 and 256 (SHA-224 and SHA-256) cryptographic
    hash functions are defined in <em>FIPS 180-4: Secure Hash Standard (SHS)</em>.
*/
/*
    Copyright (C) 2006-2018, Arm Limited (or its affiliates), All Rights Reserved
    SPDX-License-Identifier: Apache-2.0

    Licensed under the Apache License, Version 2.0 (the "License"); you may
    not use this file except in compliance with the License.
    You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

    Unless required by applicable law or agreed to in writing, software
    distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
    WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
    See the License for the specific language governing permissions and
    limitations under the License.

    This file is part of Mbed TLS (https://tls.mbed.org)
*/
#ifndef MBEDTLS_SHA256_H
#define MBEDTLS_SHA256_H

#include <stddef.h>
#include <stdint.h>



/* MBEDTLS_ERR_SHA256_HW_ACCEL_FAILED is deprecated and should not be used. */
#define MBEDTLS_ERR_SHA256_HW_ACCEL_FAILED                -0x0037  /**< SHA-256 hardware accelerator failed */
#define MBEDTLS_ERR_SHA256_BAD_INPUT_DATA                 -0x0074  /**< SHA-256 input data was malformed. */


#define mbedtls_printf(...)      printf(__VA_ARGS__)

#ifdef __cplusplus
extern "C" {
#endif


/**
    \brief          The SHA-256 context structure.

                   The structure is used both for SHA-256 and for SHA-224
                   checksum calculations. The choice between these two is
                   made in the call to mbedtls_sha256_starts_ret().
*/
typedef struct mbedtls_sha256_context
{
    uint32_t total[2];          /*!< The number of Bytes processed.  */
    uint32_t state[8];          /*!< The intermediate digest state.  */
    unsigned char buffer[64];   /*!< The data block being processed. */
    int is224;                  /*!< Determines which function to use:
                                     0: Use SHA-256, or 1: Use SHA-224. */
}
mbedtls_sha256_context;


/**
    \brief          This function initializes a SHA-256 context.

    \param ctx      The SHA-256 context to initialize. This must not be \c NULL.
*/
void mbedtls_sha256_init( mbedtls_sha256_context* ctx );

/**
    \brief          This function clears a SHA-256 context.

    \param ctx      The SHA-256 context to clear. This may be \c NULL, in which
                   case this function returns immediately. If it is not \c NULL,
                   it must point to an initialized SHA-256 context.
*/
void mbedtls_sha256_free( mbedtls_sha256_context* ctx );

/**
    \brief          This function clones the state of a SHA-256 context.

    \param dst      The destination context. This must be initialized.
    \param src      The context to clone. This must be initialized.
*/
void mbedtls_sha256_clone( mbedtls_sha256_context* dst,
                           const mbedtls_sha256_context* src );

/**
    \brief          This function starts a SHA-224 or SHA-256 checksum
                   calculation.

    \param ctx      The context to use. This must be initialized.
    \param is224    This determines which function to use. This must be
                   either \c 0 for SHA-256, or \c 1 for SHA-224.

    \return         \c 0 on success.
    \return         A negative error code on failure.
*/
int mbedtls_sha256_starts_ret( mbedtls_sha256_context* ctx, int is224 );

/**
    \brief          This function feeds an input buffer into an ongoing
                   SHA-256 checksum calculation.

    \param ctx      The SHA-256 context. This must be initialized
                   and have a hash operation started.
    \param input    The buffer holding the data. This must be a readable
                   buffer of length \p ilen Bytes.
    \param ilen     The length of the input data in Bytes.

    \return         \c 0 on success.
    \return         A negative error code on failure.
*/
int mbedtls_sha256_update_ret( mbedtls_sha256_context* ctx,
                               const unsigned char* input,
                               size_t ilen );

/**
    \brief          This function finishes the SHA-256 operation, and writes
                   the result to the output buffer.

    \param ctx      The SHA-256 context. This must be initialized
                   and have a hash operation started.
    \param output   The SHA-224 or SHA-256 checksum result.
                   This must be a writable buffer of length \c 32 Bytes.

    \return         \c 0 on success.
    \return         A negative error code on failure.
*/
int mbedtls_sha256_finish_ret( mbedtls_sha256_context* ctx,
                               unsigned char output[32] );

/**
    \brief          This function processes a single data block within
                   the ongoing SHA-256 computation. This function is for
                   internal use only.

    \param ctx      The SHA-256 context. This must be initialized.
    \param data     The buffer holding one block of data. This must
                   be a readable buffer of length \c 64 Bytes.

    \return         \c 0 on success.
    \return         A negative error code on failure.
*/
int mbedtls_internal_sha256_process( mbedtls_sha256_context* ctx,
                                     const unsigned char data[64] );



#ifdef __cplusplus
}
#endif

#endif /* mbedtls_sha256.h */

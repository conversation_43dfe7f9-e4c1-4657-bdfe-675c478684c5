/**
    \file appl_light_ctl_client.h

    \brief This file defines the Mesh Light Ctl Model Application Interface
    - includes Data Structures and Methods for Client.
*/

/*
    Copyright (C) 2018. Mindtree Ltd.
    All rights reserved.
*/

#ifndef _H_APPL_LIGHT_CTL_CLIENT_
#define _H_APPL_LIGHT_CTL_CLIENT_


/* --------------------------------------------- Header File Inclusion */
#include "MS_light_ctl_api.h"
#include "appl_main.h"


/* --------------------------------------------- Global Definitions */


/* --------------------------------------------- Data Types/ Structures */


/* --------------------------------------------- Function */
/* light_ctl client application entry point */
void main_light_ctl_client_operations(void);

/* Send Light Ctl Default Get */
void appl_send_light_ctl_default_get(void);

/* Send Light Ctl Default Set */
void appl_send_light_ctl_default_set(void);

/* Send Light Ctl Default Set Unacknowledged */
void appl_send_light_ctl_default_set_unacknowledged(void);

/* Send Light Ctl Get */
void appl_send_light_ctl_get(void);

/* Send Light Ctl Set */
void appl_send_light_ctl_set(void);

/* Send Light Ctl Set Unacknowledged */
void appl_send_light_ctl_set_unacknowledged(void);

/* Send Light Ctl Temperature Get */
void appl_send_light_ctl_temperature_get(void);

/* Send Light Ctl Temperature Range Get */
void appl_send_light_ctl_temperature_range_get(void);

/* Send Light Ctl Temperature Range Set */
void appl_send_light_ctl_temperature_range_set(void);

/* Send Light Ctl Temperature Range Set Unacknowledged */
void appl_send_light_ctl_temperature_range_set_unacknowledged(void);

/* Send Light Ctl Temperature Set */
void appl_send_light_ctl_temperature_set(void);

/* Send Light Ctl Temperature Set Unacknowledged */
void appl_send_light_ctl_temperature_set_unacknowledged(void);

/* Get Model Handle */
void appl_light_ctl_client_get_model_handle(void);

/* Set Publish Address */
void appl_light_ctl_client_set_publish_address(void);

/**
    \brief Client Application Asynchronous Notification Callback.

    \par Description
    Light_Ctl client calls the registered callback to indicate events occurred to the application.

    \param [in] handle        Model Handle.
    \param [in] opcode        Opcode.
    \param [in] data_param    Data associated with the event if any or NULL.
    \param [in] data_len      Size of the event data. 0 if event data is NULL.
*/
API_RESULT appl_light_ctl_client_cb
(
    /* IN */ MS_ACCESS_MODEL_HANDLE* handle,
    /* IN */ UINT32                   opcode,
    /* IN */ UCHAR*                   data_param,
    /* IN */ UINT16                   data_len
);

#endif /*_H_APPL_LIGHT_CTL_CLIENT_ */

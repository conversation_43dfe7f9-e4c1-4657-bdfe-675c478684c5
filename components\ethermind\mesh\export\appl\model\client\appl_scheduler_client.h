/**
    \file appl_scheduler_client.h

    \brief This file defines the Mesh Scheduler Model Application Interface
    - includes Data Structures and Methods for Client.
*/

/*
    Copyright (C) 2018. Mindtree Ltd.
    All rights reserved.
*/

#ifndef _H_APPL_SCHEDULER_CLIENT_
#define _H_APPL_SCHEDULER_CLIENT_


/* --------------------------------------------- Header File Inclusion */
#include "MS_scheduler_api.h"
#include "appl_main.h"


/* --------------------------------------------- Global Definitions */


/* --------------------------------------------- Data Types/ Structures */


/* --------------------------------------------- Function */
/* scheduler client application entry point */
void main_scheduler_client_operations(void);

/* Send Scheduler Action Get */
void appl_send_scheduler_action_get(void);

/* Send Scheduler Action Set */
void appl_send_scheduler_action_set(void);

/* Send Scheduler Action Set Unacknowledged */
void appl_send_scheduler_action_set_unacknowledged(void);

/* User input for Schedule Register fields */
void appl_input_schedule_register( /* OUT */ MS_SCHEDULER_ACTION_SET_STRUCT*   schedule_register);

/* Send Scheduler Get */
void appl_send_scheduler_get(void);

/* Get Model Handle */
void appl_scheduler_client_get_model_handle(void);

/* Set Publish Address */
void appl_scheduler_client_set_publish_address(void);

/**
    \brief Client Application Asynchronous Notification Callback.

    \par Description
    Scheduler client calls the registered callback to indicate events occurred to the application.

    \param [in] handle        Model Handle.
    \param [in] opcode        Opcode.
    \param [in] data_param    Data associated with the event if any or NULL.
    \param [in] data_len      Size of the event data. 0 if event data is NULL.
*/
API_RESULT appl_scheduler_client_cb
(
    /* IN */ MS_ACCESS_MODEL_HANDLE* handle,
    /* IN */ UINT32                   opcode,
    /* IN */ UCHAR*                   data_param,
    /* IN */ UINT16                   data_len
);

#endif /*_H_APPL_SCHEDULER_CLIENT_ */

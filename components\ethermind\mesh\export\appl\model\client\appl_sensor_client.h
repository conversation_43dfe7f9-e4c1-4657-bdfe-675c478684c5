/**
    \file appl_sensor_client.h

    \brief This file defines the Mesh Sensor Model Application Interface
    - includes Data Structures and Methods for Client.
*/

/*
    Copyright (C) 2018. Mindtree Ltd.
    All rights reserved.
*/

#ifndef _H_APPL_SENSOR_CLIENT_
#define _H_APPL_SENSOR_CLIENT_


/* --------------------------------------------- Header File Inclusion */
#include "MS_sensor_api.h"
#include "appl_main.h"


/* --------------------------------------------- Global Definitions */


/* --------------------------------------------- Data Types/ Structures */


/* --------------------------------------------- Function */
/* sensor client application entry point */
void main_sensor_client_operations(void);

/* Send Sensor Cadence Get */
void appl_send_sensor_cadence_get(void);

/* Send Sensor Cadence Set */
void appl_send_sensor_cadence_set(void);

/* Send Sensor Cadence Set Unacknowledged */
void appl_send_sensor_cadence_set_unacknowledged(void);

/* Send Sensor Column Get */
void appl_send_sensor_column_get(void);

/* Send Sensor Descriptor Get */
void appl_send_sensor_descriptor_get(void);

/* Send Sensor Get */
void appl_send_sensor_get(void);

/* Send Sensor Series Get */
void appl_send_sensor_series_get(void);

/* Send Sensor Setting Get */
void appl_send_sensor_setting_get(void);

/* Send Sensor Setting Set */
void appl_send_sensor_setting_set(void);

/* Send Sensor Setting Set Unacknowledged */
void appl_send_sensor_setting_set_unacknowledged(void);

/* Send Sensor Settings Get */
void appl_send_sensor_settings_get(void);

/* Get Model Handle */
void appl_sensor_client_get_model_handle(void);

/* Set Publish Address */
void appl_sensor_client_set_publish_address(void);

/**
    \brief Client Application Asynchronous Notification Callback.

    \par Description
    Sensor client calls the registered callback to indicate events occurred to the application.

    \param [in] handle        Model Handle.
    \param [in] opcode        Opcode.
    \param [in] data_param    Data associated with the event if any or NULL.
    \param [in] data_len      Size of the event data. 0 if event data is NULL.
*/
API_RESULT appl_sensor_client_cb
(
    /* IN */ MS_ACCESS_MODEL_HANDLE* handle,
    /* IN */ UINT32                   opcode,
    /* IN */ UCHAR*                   data_param,
    /* IN */ UINT16                   data_len
);

#endif /*_H_APPL_SENSOR_CLIENT_ */

/**
    \file cli_light_hsl_client.h

    \brief This file defines the Mesh Light Hsl Model Application Interface
    - includes Data Structures and Methods for Client.
*/

/*
    Copyright (C) 2018. Mindtree Ltd.
    All rights reserved.
*/

#ifndef _H_CLI_LIGHT_HSL_CLIENT_
#define _H_CLI_LIGHT_HSL_CLIENT_


/* --------------------------------------------- Header File Inclusion */
#include "MS_light_hsl_api.h"
#include "cli_main.h"


/* --------------------------------------------- Global Definitions */


/* --------------------------------------------- Data Types/ Structures */


/* --------------------------------------------- Function */
/* light_hsl client CLI entry point */
CLI_CMD_HANDLER_DECL(cli_modelc_light_hsl);

/* light_hsl client CLI entry point */
CLI_CMD_HANDLER_DECL(cli_modelc_light_hsl_setup);

/* Send Light Hsl Default Get */
CLI_CMD_HANDLER_DECL(cli_modelc_light_hsl_default_get);

/* Send Light Hsl Default Set */
CLI_CMD_HANDLER_DECL(cli_modelc_light_hsl_default_set);

/* Send Light Hsl Default Set Unacknowledged */
CLI_CMD_HANDLER_DECL(cli_modelc_light_hsl_default_set_unacknowledged);

/* Send Light Hsl Get */
CLI_CMD_HANDLER_DECL(cli_modelc_light_hsl_get);

/* Send Light Hsl Hue Get */
CLI_CMD_HANDLER_DECL(cli_modelc_light_hsl_hue_get);

/* Send Light Hsl Hue Set */
CLI_CMD_HANDLER_DECL(cli_modelc_light_hsl_hue_set);

/* Send Light Hsl Hue Set Unacknowledged */
CLI_CMD_HANDLER_DECL(cli_modelc_light_hsl_hue_set_unacknowledged);

/* Send Light Hsl Range Get */
CLI_CMD_HANDLER_DECL(cli_modelc_light_hsl_range_get);

/* Send Light Hsl Range Set */
CLI_CMD_HANDLER_DECL(cli_modelc_light_hsl_range_set);

/* Send Light Hsl Range Set Unacknowledged */
CLI_CMD_HANDLER_DECL(cli_modelc_light_hsl_range_set_unacknowledged);

/* Send Light Hsl Saturation Get */
CLI_CMD_HANDLER_DECL(cli_modelc_light_hsl_saturation_get);

/* Send Light Hsl Saturation Set */
CLI_CMD_HANDLER_DECL(cli_modelc_light_hsl_saturation_set);

/* Send Light Hsl Saturation Set Unacknowledged */
CLI_CMD_HANDLER_DECL(cli_modelc_light_hsl_saturation_set_unacknowledged);

/* Send Light Hsl Set */
CLI_CMD_HANDLER_DECL(cli_modelc_light_hsl_set);

/* Send Light Hsl Set Unacknowledged */
CLI_CMD_HANDLER_DECL(cli_modelc_light_hsl_set_unacknowledged);

/* Send Light Hsl Target Get */
CLI_CMD_HANDLER_DECL(cli_modelc_light_hsl_target_get);

/* Get Model Handle */
CLI_CMD_HANDLER_DECL(cli_modelc_light_hsl_get_model_handle);

/* Set Publish Address */
CLI_CMD_HANDLER_DECL(cli_modelc_light_hsl_set_publish_address);

/**
    \brief Client Application Asynchronous Notification Callback.

    \par Description
    Light_Hsl client calls the registered callback to indicate events occurred to the application.

    \param [in] handle        Model Handle.
    \param [in] opcode        Opcode.
    \param [in] data_param    Data associated with the event if any or NULL.
    \param [in] data_len      Size of the event data. 0 if event data is NULL.
*/
API_RESULT cli_light_hsl_client_cb
(
    /* IN */ MS_ACCESS_MODEL_HANDLE* handle,
    /* IN */ UINT32                   opcode,
    /* IN */ UCHAR*                   data_param,
    /* IN */ UINT16                   data_len
);

#endif /*_H_CLI_LIGHT_HSL_CLIENT_ */

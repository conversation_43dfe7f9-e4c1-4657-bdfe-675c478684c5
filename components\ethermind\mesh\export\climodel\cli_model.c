/**************************************************************************************************

    Phyplus Microelectronics Limited confidential and proprietary.
    All rights reserved.

    IMPORTANT: All rights of this software belong to Phyplus Microelectronics
    Limited ("Phyplus"). Your use of this Software is limited to those
    specific rights granted under  the terms of the business contract, the
    confidential agreement, the non-disclosure agreement and any other forms
    of agreements as a customer or a partner of Phyplus. You may not use this
    Software unless you agree to abide by the terms of these agreements.
    You acknowledge that the Software may not be modified, copied,
    distributed or disclosed unless embedded on a Phyplus Bluetooth Low Energy
    (BLE) integrated circuit, either as a product or is integrated into your
    products.  Other than for the aforementioned purposes, you may not use,
    reproduce, copy, prepare derivative works of, modify, distribute, perform,
    display or sell this Software and/or its documentation for any purposes.

    YOU FURTHER ACKNOWLEDGE AND AGREE THAT THE SOFTWARE AND DOCUMENTATION ARE
    PROVIDED AS IS WITHOUT WARRANTY OF ANY KIND, EITHER EXPRESS OR IMPLIED,
    INCLUDING WITHOUT LIMITATION, ANY WARRANTY OF MERCHANTABILITY, TITLE,
    NON-INFRINGEMENT AND FITNESS FOR A PARTICULAR PURPOSE. IN NO EVENT SHALL
    PHYPLUS OR ITS SUBSIDIARIES BE LIABLE OR OBLIGATED UNDER CONTRACT,
    NEGLIGENCE, STRICT LIABILITY, CONTRIBUTION, BREACH OF WARRANTY, OR OTHER
    LEGAL EQUITABLE THEORY ANY DIRECT OR INDIRECT DAMAGES OR EXPENSES
    INCLUDING BUT NOT LIMITED TO ANY INCIDENTAL, SPECIAL, INDIRECT, PUNITIVE
    OR CONSEQUENTIAL DAMAGES, LOST PROFITS OR LOST DATA, COST OF PROCUREMENT
    OF SUBSTITUTE GOODS, TECHNOLOGY, SERVICES, OR ANY CLAIMS BY THIRD PARTIES
    (INCLUDING BUT NOT LIMITED TO ANY DEFENSE THEREOF), OR OTHER SIMILAR COSTS.

**************************************************************************************************/

/*********************************************************************
    INCLUDES
*/

#include "cli_model.h"
#include "vendormodel_server.h"
#include "ltrn_extern.h"
#include "cliface.h"
#include "access_internal.h"

#define CONSOLE_OUT(...) printf(__VA_ARGS__)
#define CONSOLE_IN(...) scanf(__VA_ARGS__)

extern CLI_COMMAND *g_cli_cmd_list;
extern uint8_t      g_cli_cmd_len;

/*********************************************************************
    EXTERNAL VARIABLES
*/
extern EM_timer_handle      thandle;
extern uint8                llState;
extern uint8                llSecondaryState;
extern llGlobalStatistics_t g_pmCounters;
extern UCHAR                blebrr_state;
extern uint32               blebrr_advscan_timeout_count;
extern UINT32               blebrr_scanTimeOut;

extern llGlobalStatistics_t g_pmCounters;
// extern uint32_t g_stop_scan_t1;
// extern uint32_t g_stop_scan_t1_err;
// extern uint8_t llModeDbg[6];

extern PROV_DEVICE_S UI_lprov_device;

/*********************************************************************
    EXTERNAL FUNCTIONS
*/

#if (BLEMESH_ROLE == PROV_ROLE_PROVISIONER)
#else
extern MS_ACCESS_MODEL_HANDLE UI_vendor_defined_server_model_handle;
extern API_RESULT             UI_sample_get_net_key(void);
extern API_RESULT             UI_sample_get_device_key(void);
extern API_RESULT             UI_sample_check_app_key(void);

extern void timeout_cb(void *args, UINT16 size);

/*********************************************************************
    LOCAL FUNCTIONS
*/

/**
 * @brief 发送 Vendor Model 可靠 PDU
 * @description 通过 Vendor Model 发送可靠的 PDU 数据包到指定目标地址
 * @param req_opcode 请求操作码，支持 MS_ACCESS_VENDORMODEL_WRITECMD_OPCODE
 * @param dest_addr 目标地址（16位）
 * @param appKey_index 应用密钥索引
 * @param param 要发送的参数数据
 * @param len 数据长度
 * @return API_SUCCESS 成功，API_FAILURE 失败
 * @note 目前只支持 WRITECMD 操作码，其他操作码会被忽略
 */
static API_RESULT cli_vendormodel_send_reliable_pdu(
    /* IN */ UINT32 req_opcode,
    /* IN */ UINT16 dest_addr,
    /* IN */ UINT16 appKey_index,
    /* IN */ void  *param,
    /* IN */ UINT16 len)
{
    API_RESULT retval;
    /* TODO: Check what should be maximum length */
    UCHAR  buffer[384];
    UCHAR *pdu_ptr;
    UINT16 marker;
    retval = API_FAILURE;
    marker = 0;

    switch(req_opcode)
    {
        case MS_ACCESS_VENDORMODEL_WRITECMD_OPCODE: {
            EM_mem_copy(&buffer[marker], param, len);
            marker += len;
        }
        break;

        default:
            break;
    }

    /* Publish - reliable */
    if(0 == marker) { pdu_ptr = NULL; }
    else { pdu_ptr = buffer; }

    retval = MS_access_raw_data(&UI_vendor_defined_server_model_handle, req_opcode, dest_addr, appKey_index, pdu_ptr,
                                marker, MS_FALSE);
    return retval;
}

/**
 * @brief CLI 原始数据发送命令
 * @description 通过 CLI 命令发送原始数据到指定的 mesh 节点
 * @param argc 参数个数，至少需要4个参数
 * @param argv 参数数组：
 *             argv[0] - 目标地址（16进制字符串）
 *             argv[1] - 应用密钥索引（16进制字符串）
 *             argv[2] - 数据长度（16进制字符串）
 *             argv[3] - 原始数据（16进制字符串）
 * @return API_SUCCESS 成功，API_FAILURE 失败
 * @note 仅在 Device 角色下可用，数据长度不能为0
 * @example cli_raw_data 1234 0 8 0102030405060708
 */
API_RESULT cli_raw_data(UINT32 argc, UCHAR *argv[])
{
    UINT16 dst_addr;
    UINT16 data_len;
    UINT32 opcode   = 0x00;
    UINT8  sendmode = 0x00;
    UCHAR  buffer[256]; /* 最大支持256字节数据 */

    if(data_len < 3)
    {
        printf("Invalid data format\n");
        return API_FAILURE;
    }

    for(size_t i = 0; i < argc; i++) { log_printf("\targv[%d]: %s", i, argv[i]); }

    data_len = CLI_strtoi(argv[0], CLI_strlen(argv[0]), 16);
    dst_addr = CLI_strtoi(argv[1], CLI_strlen(argv[1]), 16);
    sendmode = CLI_strtoi(argv[2], CLI_strlen(argv[2]), 8);
    opcode   = MS_ACCESS_VENDORMODEL_GET_OPCODE + ((sendmode & 0x0f) << 16);
    if(CLI_strtoarray(argv[3], CLI_strlen(argv[3]), buffer, data_len) != API_SUCCESS) return API_FAILURE;

    cli_vendormodel_send_reliable_pdu(opcode, dst_addr, 0, buffer, data_len);

    printf("Sent to 0x%04X, opcode 0x%02x,data_len %d, \n", dst_addr, opcode, data_len);

    return API_SUCCESS;
}
#endif

/**
 * @brief 获取设备信息
 * @description 获取并显示当前设备的主要单播地址和功能特性
 * @param argc 参数个数（未使用）
 * @param argv 参数数组（未使用）
 * @return API_SUCCESS 成功，API_FAILURE 失败
 * @note 输出格式：<地址><特性>，用于设备信息查询
 */
API_RESULT cli_get_information(UINT32 argc, UCHAR *argv[])
{
    API_RESULT  retval;
    MS_NET_ADDR addr;
    UINT8       features;
    MS_IGNORE_UNUSED_PARAM(argc);
    MS_IGNORE_UNUSED_PARAM(argv);
    retval = MS_access_cm_get_primary_unicast_address(&addr);

    if(retval != API_SUCCESS) { return API_FAILURE; }

    MS_access_cm_get_features(&features);
    dbg_printf("addr:%04X-features:%02X", addr, features);
    return API_SUCCESS;
}

/**
 * @brief 清除 Mesh 协议栈配置
 * @description 根据设备角色清除 Mesh 协议栈的配置信息：
 *              - Provisioner: 重置协议栈状态
 *              - Device: 重置协议栈状态，停止扫描，处理 Proxy 连接
 * @param argc 参数个数（未使用）
 * @param argv 参数数组（未使用）
 * @return API_SUCCESS 成功
 * @note 此函数不会清除持久化存储，只重置运行时状态
 *       Device 角色下会根据 Proxy 连接状态决定是否断开连接或启动定时器
 */
API_RESULT cli_mesh_stack_clear(UINT32 argc, UCHAR *argv[])
{
    MS_IGNORE_UNUSED_PARAM(argc);
    MS_IGNORE_UNUSED_PARAM(argv);
    printf("cli mesh stack clear\n");
#if (BLEMESH_ROLE == PROV_ROLE_PROVISIONER)
    MS_access_cm_reset(PROV_ROLE_PROVISIONER);
#else
    UCHAR proxy_state;
    MS_proxy_fetch_state(&proxy_state);
    MS_access_cm_reset(PROV_ROLE_DEVICE);
    blebrr_scan_pl(FALSE);

    if(MS_PROXY_CONNECTED != proxy_state) { EM_start_timer(&thandle, 3, timeout_cb, NULL, 0); }
    else { blebrr_disconnect_pl(); }

#endif
    return API_SUCCESS;
}
#if (BLEMESH_ROLE == PROV_ROLE_PROVISIONER)
#else
/**
 * @brief 显示密钥信息
 * @description 显示当前设备的网络密钥、设备密钥和应用密钥信息
 * @param argc 参数个数（未使用）
 * @param argv 参数数组（未使用）
 * @return API_SUCCESS 成功
 * @note 仅在 Device 角色下可用，用于调试和查看密钥状态
 */
API_RESULT cli_disp_key(UINT32 argc, UCHAR *argv[])
{
    MS_IGNORE_UNUSED_PARAM(argc);
    MS_IGNORE_UNUSED_PARAM(argv);
    UI_sample_get_net_key();
    UI_sample_get_device_key();
    UI_sample_check_app_key();
    return API_SUCCESS;
}

/**
 * @brief 发送测试 PDU
 * @description 发送指定长度的测试数据到目标地址，并显示内存使用统计
 * @param argc 参数个数，需要3个参数
 * @param argv 参数数组：
 *             argv[0] - 目标地址（16进制字符串）
 *             argv[1] - 应用密钥索引（16进制字符串）
 *             argv[2] - 数据长度（16进制字符串，1-384字节）
 * @return API_SUCCESS 成功，API_FAILURE 失败
 * @note 仅在 Device 角色下可用，发送的数据为递增序列（0,1,2,3...）
 *       会在发送前后显示内存使用统计信息
 */
API_RESULT cli_send_pdu(UINT32 argc, UCHAR *argv[])
{
    UINT16 dst_address;
    UINT16 data_len, appKeyIndex;
    UINT8  buffer[384];

    if(argc < 3)
    {
        printf("Invaild RAW DATA Paraments\n");
        return API_FAILURE;
    }

    dst_address = CLI_strtoi(argv[0], CLI_strlen(argv[0]), 16);
    appKeyIndex = CLI_strtoi(argv[1], CLI_strlen(argv[1]), 16);
    data_len    = CLI_strtoi(argv[2], CLI_strlen(argv[2]), 16);

    if((data_len == 0) || data_len > 384)
    {
        printf("RAW DATA INVALID,Return\n");
        return API_FAILURE;
    }

    for(int i = 0; i < data_len; i++) { buffer[i] = i; }

    printf("-before-\n");
#if (MESH_HEAP == 1)
    extern uint32 mesh_osal_memory_statics(void);
    mesh_osal_memory_statics();
#else
    extern uint32 osal_memory_statics(void);
    osal_memory_statics();
#endif
    cli_vendormodel_send_reliable_pdu(MS_ACCESS_VENDORMODEL_WRITECMD_OPCODE, dst_address, appKeyIndex, buffer,
                                      data_len);
    printf("-after-\n");
#if (MESH_HEAP == 1)
    mesh_osal_memory_statics();
#else
    osal_memory_statics();
#endif
    printf("DST 0x%04X data_len 0x%02X\n", dst_address, data_len);
    return API_SUCCESS;
}

#endif

/**
 * @brief 显示链路层连接信息
 * @description 显示详细的链路层性能计数器和统计信息
 * @param 无参数
 * @return 无返回值
 * @note 显示包括广告、扫描、连接等各种链路层操作的统计计数
 */
static void ll_dumpConnectionInfo(void)
{
    printf("========== LL PM counters ================\r\n");
    printf("ll_send_undirect_adv_cnt = %d\r\n", g_pmCounters.ll_send_undirect_adv_cnt);
    printf("ll_send_nonconn_adv_cnt = %d\r\n", g_pmCounters.ll_send_nonconn_adv_cnt);
    printf("ll_send_scan_adv_cnt = %d\r\n", g_pmCounters.ll_send_scan_adv_cnt);
    printf("ll_send_hdc_dir_adv_cnt = %d\r\n", g_pmCounters.ll_send_hdc_dir_adv_cnt);
    printf("ll_send_ldc_dir_adv_cnt = %d\r\n", g_pmCounters.ll_send_ldc_dir_adv_cnt);
    printf("ll_send_conn_adv_cnt = %d\r\n", g_pmCounters.ll_send_conn_adv_cnt);
    printf("ll_conn_adv_pending_cnt = %d\r\n", g_pmCounters.ll_conn_adv_pending_cnt);
    printf("ll_conn_scan_pending_cnt = %d\r\n", g_pmCounters.ll_conn_scan_pending_cnt);
    printf("ll_recv_scan_req_cnt = %d\r\n", g_pmCounters.ll_recv_scan_req_cnt);
    printf("ll_send_scan_rsp_cnt = %d\r\n", g_pmCounters.ll_send_scan_rsp_cnt);
    printf("ll_recv_conn_req_cnt = %d\r\n", g_pmCounters.ll_recv_conn_req_cnt);
    printf("ll_send_conn_rsp_cnt = %d\r\n", g_pmCounters.ll_send_conn_rsp_cnt);
    printf("ll_filter_scan_req_cnt = %d\r\n", g_pmCounters.ll_filter_scan_req_cnt);
    printf("ll_filter_conn_req_cnt = %d\r\n", g_pmCounters.ll_filter_conn_req_cnt);
    printf("ll_recv_adv_pkt_cnt = %d\r\n", g_pmCounters.ll_recv_adv_pkt_cnt);
    printf("ll_send_scan_req_cnt = %d\r\n", g_pmCounters.ll_send_scan_req_cnt);
    printf("ll_recv_scan_rsp_cnt = %d\r\n", g_pmCounters.ll_recv_scan_rsp_cnt);
    printf("ll_conn_succ_cnt = %d\r\n", g_pmCounters.ll_conn_succ_cnt);
    printf("ll_link_lost_cnt = %d\r\n", g_pmCounters.ll_link_lost_cnt);
    printf("ll_link_estab_fail_cnt = %d\r\n", g_pmCounters.ll_link_estab_fail_cnt);
    printf("ll_rx_peer_cnt = %d\r\n", g_pmCounters.ll_rx_peer_cnt);
    printf("ll_evt_shc_err = %d\r\n", g_pmCounters.ll_evt_shc_err);
    printf("ll_trigger_err = %d\r\n", g_pmCounters.ll_trigger_err);
    printf("ll_rfifo_rst_err = %d\r\n", g_pmCounters.ll_rfifo_rst_err);
    printf("ll_rfifo_rst_cnt = %d\r\n", g_pmCounters.ll_rfifo_rst_cnt);
    printf("ll_rfifo_read_err = %d\r\n", g_pmCounters.ll_rfifo_read_err);
    printf("\r\n ");
}
/**
 * @brief 完全重置设备配网信息
 * @description 根据设备角色执行完全重置：
 *              - Provisioner: 重置协议栈，清空已配网设备列表，保存设备密钥记录
 *              - Device: 停止扫描，清除整个持久化存储，重置协议栈，启动定时器
 * @param argc 参数个数（未使用）
 * @param argv 参数数组（未使用）
 * @return API_SUCCESS 成功
 * @note ⚠️ Device 角色下会清除整个 NVM 存储区域，包括所有配置信息
 *       如果 Proxy 已连接会主动断开连接
 *       重置后设备回到未配网状态
 */
API_RESULT cli_demo_reset(UINT32 argc, UCHAR *argv[])
{
    UCHAR proxy_state, proxy;
    MS_access_cm_get_features_field(&proxy, MS_FEATURE_PROXY);

    if(MS_TRUE == proxy)
    {
        MS_proxy_fetch_state(&proxy_state);

        if(proxy_state == MS_PROXY_CONNECTED) blebrr_disconnect_pl();
    }

#if (BLEMESH_ROLE == PROV_ROLE_PROVISIONER)
    MS_access_cm_reset(PROV_ROLE_PROVISIONER);
    extern MS_PROV_DEV_ENTRY g_prov_dev_list[MS_MAX_DEV_KEYS];

    for(int i = 0; i < MS_MAX_DEV_KEYS; i++) EM_mem_set(&g_prov_dev_list[i], 0, sizeof(MS_PROV_DEV_ENTRY));

    ms_access_ps_store(MS_PS_RECORD_DEV_KEYS);
#else
    blebrr_scan_pl(FALSE);
    nvs_reset(NVS_BANK_PERSISTENT);
    MS_access_cm_reset(PROV_ROLE_DEVICE);

    if(thandle == EM_TIMER_HANDLE_INIT_VAL) { EM_start_timer(&thandle, 3, timeout_cb, NULL, 0); }

#endif
    // printf ("Done\r\n");
    CLIT_output("[%s,%d]", "reset", API_SUCCESS);
    return API_SUCCESS;
}

/**
 * @brief 显示内部状态信息
 * @description 显示系统内部状态，包括链路层状态、BLE Bearer 状态和连接统计信息
 * @param argc 参数个数（未使用）
 * @param argv 参数数组（未使用）
 * @return API_SUCCESS 成功
 * @note 用于调试和监控系统运行状态，显示链路层和 BLE 相关的状态信息
 */
API_RESULT cli_internal_status(UINT32 argc, UCHAR *argv[])
{
    MS_IGNORE_UNUSED_PARAM(argc);
    MS_IGNORE_UNUSED_PARAM(argv);
    printf("\r\n===== internal status ============\r\n");
    printf("llState = %d, llSecondaryState = %d\r\n", llState, llSecondaryState);
    printf("blebrr_state = %d\r\n", blebrr_state);
    printf("blebrr_scanTimOut = %d\r\n", blebrr_scanTimeOut);
    printf("\r\n");
    ll_dumpConnectionInfo();
    return API_SUCCESS;
}

/**
 * @brief 发送配置心跳发布设置命令
 * @description 向目标节点发送心跳发布配置，设置心跳消息的发布参数
 * @param argc 参数个数，需要6个参数
 * @param argv 参数数组：
 *             argv[0] - 目标地址（16进制）
 *             argv[1] - 计数日志（8位16进制）
 *             argv[2] - 周期日志（8位16进制）
 *             argv[3] - TTL值（8位16进制）
 *             argv[4] - 功能特性（16位16进制）
 *             argv[5] - 网络密钥索引（16位16进制）
 * @return API_SUCCESS 成功，API_FAILURE 失败
 * @note 用于配置节点的心跳发布功能，监控网络状态
 */
API_RESULT cli_modelc_config_heartbeat_publication_set(UINT32 argc, UCHAR *argv[])
{
    API_RESULT                           retval;
    int                                  choice;
    ACCESS_CONFIG_HEARTBEATPUB_SET_PARAM param;
    CONSOLE_OUT(">> Send Config Heartbeat Publication Set\n");

    if(6 == argc)
    {
        choice            = CLI_strtoi(argv[0], CLI_strlen(argv[0]), 16);
        param.destination = (UINT16)choice;
        CONSOLE_OUT("Destination (16-bit in HEX): 0x%04X\n", param.destination);
        choice         = CLI_strtoi(argv[1], CLI_strlen(argv[1]), 16);
        param.countlog = (UCHAR)choice;
        CONSOLE_OUT("CountLog (8-bit in HEX): 0x%02X\n", param.countlog);
        choice          = CLI_strtoi(argv[2], CLI_strlen(argv[2]), 16);
        param.periodlog = (UCHAR)choice;
        CONSOLE_OUT("PeriodLog (8-bit in HEX): 0x%02X\n", param.periodlog);
        choice    = CLI_strtoi(argv[3], CLI_strlen(argv[3]), 16);
        param.ttl = (UCHAR)choice;
        CONSOLE_OUT("TTL (8-bit in HEX): 0x%02X\n", param.ttl);
        choice         = CLI_strtoi(argv[4], CLI_strlen(argv[4]), 16);
        param.features = (UINT16)choice;
        CONSOLE_OUT("Features (16-bit in HEX): 0x%04X\n", param.features);
        choice             = CLI_strtoi(argv[5], CLI_strlen(argv[5]), 16);
        param.netkey_index = (UINT16)choice;
        CONSOLE_OUT("NetKeyIndex (16-bit in HEX): 0x%04X\n", param.netkey_index);
    }
    else
    {
        CONSOLE_OUT("Invalid Number of Arguments:0x%04X. Returning.\n", argc);
        return API_FAILURE;
    }

    retval = MS_config_client_heartbeat_publication_set(&param);
    CONSOLE_OUT("retval = 0x%04X\n", retval);
    return retval;
}

/**
 * @brief 开启命令（预留）
 * @description 预留的开启功能命令，当前未实现具体功能
 * @param argc 参数个数（未使用）
 * @param argv 参数数组（未使用）
 * @return API_SUCCESS 成功
 * @note 预留接口，可用于实现通用开关控制功能
 */
API_RESULT cli_on(UINT32 argc, UCHAR *argv[])
{
    //    UI_generic_onoff_set(0x01);
    return API_SUCCESS;
}

/**
 * @brief 关闭命令（预留）
 * @description 预留的关闭功能命令，当前未实现具体功能
 * @param argc 参数个数（未使用）
 * @param argv 参数数组（未使用）
 * @return API_SUCCESS 成功
 * @note 预留接口，可用于实现通用开关控制功能
 */
API_RESULT cli_off(UINT32 argc, UCHAR *argv[])
{
    //    UI_generic_onoff_set(0x00);
    return API_SUCCESS;
}

/**
 * @brief 设置 UUID 字节（预留）
 * @description 预留的 UUID 设置函数，当前未实现具体功能
 * @param uuid_0 UUID 的第一个字节
 * @return 无返回值
 * @note 预留接口，可用于设置设备 UUID 的特定字节
 */
static void set_uuid_octet(UCHAR uuid_0)
{}

/**
 * @brief 启动命令
 * @description 设置 UUID 字节并启动相关功能（当前部分功能被注释）
 * @param argc 参数个数，需要1个参数
 * @param argv 参数数组：
 *             argv[0] - UUID 第一个字节（16进制）
 * @return API_SUCCESS 成功，API_FAILURE 失败
 * @note 用于设置设备 UUID 并启动 mesh 示例应用
 */
API_RESULT cli_start(UINT32 argc, UCHAR *argv[])
{
    int val;

    if(1 != argc)
    {
        printf("Usage: start <octet_0>\r\n");
        return API_FAILURE;
    }

    val = CLI_strtoi(argv[0], CLI_strlen(argv[0]), 16);
    set_uuid_octet((UCHAR)val);
    //    appl_mesh_sample();
    return API_SUCCESS;
}

/**
 * @brief 选择 Mesh 组
 * @description 选择指定的 Mesh 组索引（当前仅显示信息，具体行为待实现）
 * @param argc 参数个数，需要1个参数
 * @param argv 参数数组：
 *             argv[0] - 组索引（16进制）
 * @return API_SUCCESS 成功，API_FAILURE 失败
 * @note 用于选择特定的 Mesh 组，具体行为需要进一步实现
 */
API_RESULT cli_group_select(UINT32 argc, UCHAR *argv[])
{
    UINT16 group;

    if(1 != argc)
    {
        printf("Usage: group <Group Idx>\r\n");
        return API_FAILURE;
    }

    group = CLI_strtoi(argv[0], CLI_strlen(argv[0]), 16);
    printf("select Mesh Group %d, behavior to be implemented\r\n", group);
    return API_SUCCESS;
}

/**
 * @brief 发送配置密钥刷新阶段设置命令
 * @description 设置网络密钥的刷新阶段状态
 * @param argc 参数个数，需要2个参数
 * @param argv 参数数组：
 *             argv[0] - 网络密钥索引（16进制）
 *             argv[1] - 转换阶段（8位16进制）
 * @return API_SUCCESS 成功，API_FAILURE 失败
 * @note 用于密钥刷新过程中的阶段控制
 */
API_RESULT cli_core_modelc_config_key_refresh_phase_set(UINT32 argc, UCHAR *argv[])
{
    API_RESULT                               retval;
    int                                      choice;
    ACCESS_CONFIG_KEYREFRESH_PHASE_SET_PARAM param;
    printf(">> Send Config Key Refresh Phase Set\n");

    if(2 == argc)
    {
        choice             = CLI_strtoi(argv[0], CLI_strlen(argv[0]), 16);
        param.netkey_index = (UINT16)choice;
        printf("NetKeyIndex (16-bit in HEX): 0x%04X\n", param.netkey_index);
        choice           = CLI_strtoi(argv[1], CLI_strlen(argv[1]), 16);
        param.transition = (UCHAR)choice;
        printf("Transition (8-bit in HEX): 0x%02X\n", param.transition);
    }
    else
    {
        printf("Invalid Number of Arguments:0x%04X. Returning.\n", argc);
        return API_FAILURE;
    }

    /* Change Local State as well */
    MS_access_cm_set_key_refresh_phase(0,                /* subnet_handle */
                                       &param.transition /* key_refresh_state */
    );
    param.transition = (UCHAR)choice;
    retval           = MS_config_client_keyrefresh_phase_set(&param);
    printf("retval = 0x%04X\n", retval);
    return retval;
}

/**
 * @brief 发送配置网络密钥更新命令
 * @description 更新指定索引的网络密钥
 * @param argc 参数个数，需要2个参数
 * @param argv 参数数组：
 *             argv[0] - 网络密钥索引（16进制）
 *             argv[1] - 新的网络密钥（16字节16进制字符串）
 * @return API_SUCCESS 成功，API_FAILURE 失败
 * @note 用于网络密钥的更新操作，同时更新本地和远程密钥
 */
API_RESULT cli_core_modelc_config_netkey_update(UINT32 argc, UCHAR *argv[])
{
    API_RESULT                        retval;
    int                               choice;
    ACCESS_CONFIG_NETKEY_UPDATE_PARAM param;
    printf(">> Send Config Netkey Update\n");

    if(2 == argc)
    {
        choice             = CLI_strtoi(argv[0], CLI_strlen(argv[0]), 16);
        param.netkey_index = (UINT16)choice;
        printf("NetKeyIndex (16-bit in HEX): 0x%04X\n", param.netkey_index);
        CLI_strtoarray(argv[1], CLI_strlen(argv[1]), &param.netkey[0], 16);
    }
    else
    {
        printf("Invalid Number of Arguments:0x%04X. Returning.\n", argc);
        return API_FAILURE;
    }

    /* Set Local NetKey */
    MS_access_cm_add_update_netkey(0,                                     /* netkey_index */
                                   MS_ACCESS_CONFIG_NETKEY_UPDATE_OPCODE, /* opcode */
                                   &param.netkey[0]                       /* net_key */
    );
    retval = MS_config_client_netkey_update(&param);
    printf("retval = 0x%04X\n", retval);
    return retval;
}

/**
 * @brief 设置序列号和 IV 索引
 * @description 根据不同模式设置网络序列号、IV 索引和 SNB 功能
 * @param argc 参数个数，需要3个参数
 * @param argv 参数数组：
 *             argv[0] - 自动标志（16进制）：0=设置序列号，1=设置IV索引，2=控制SNB功能
 *             argv[1] - IV 索引值（10进制）或 SNB 控制（0=禁用，1=启用）
 *             argv[2] - IV 标志（16进制）
 * @return API_SUCCESS 成功
 * @note 用于网络安全参数的配置和 SNB（Secure Network Beacon）功能控制
 */
API_RESULT cli_snb(UINT32 argc, UCHAR *argv[])
{
    extern NET_SEQ_NUMBER_STATE net_seq_number_state;
    printf("Set sequnce number\r\n");
    UINT8 auto_flag, ivindex, ivflag;
    auto_flag = CLI_strtoi(argv[0], CLI_strlen(argv[0]), 16);
    ivindex   = CLI_strtoi(argv[1], CLI_strlen(argv[1]), 10);
    ivflag    = CLI_strtoi(argv[2], CLI_strlen(argv[2]), 16);

    if(auto_flag == 0)
    {
        net_seq_number_state.seq_num           = 0xEFFFF0;
        net_seq_number_state.block_seq_num_max = 0xEFFFF0;
    }
    else if(auto_flag == 1)
    {
        ms_iv_index.iv_index = ivindex;

        if(ivflag == 0x00) { ms_iv_index.iv_update_state &= ivflag; }
        else { ms_iv_index.iv_update_state |= ivflag; }
    }
    else if(auto_flag == 2)
    {
        if(ivindex == 0)
        {
            MS_DISABLE_SNB_FEATURE();
            MS_net_stop_snb_timer(0);
        }
        else
        {
            MS_ENABLE_SNB_FEATURE();
            MS_net_start_snb_timer(0);
        }
    }

    MS_access_ps_store_all_record();
    printf("Done\r\n");
    return API_SUCCESS;
}

MS_NET_HEADER g_hdr;
UCHAR         g_hdr_flag = MS_FALSE;

/**
 * @brief 发送分段测试数据
 * @description 发送分段或非分段的测试数据到指定目标地址
 * @param argc 参数个数，需要2个参数
 * @param argv 参数数组：
 *             argv[0] - 目标地址（16进制）
 *             argv[1] - 模式标志（0=非分段5字节，1=分段15字节）
 * @return API_SUCCESS 成功，API_FAILURE 失败
 * @note 用于测试 mesh 网络的分段传输功能
 */
API_RESULT cli_seg_send(UINT32 argc, UCHAR *argv[])
{
    UINT16     dst_addr;
    UCHAR      mode;
    API_RESULT retval;
    UINT16     len;
    UCHAR     *trans_pdu;
    /* Un-Segmented Data of 5 Bytes */
    DECL_CONST UCHAR unseg_trans_pdu[] = {0x01, 0x02, 0x03, 0x04, 0x05};
    /* Segmented Data of 15 Bytes */
    DECL_CONST UCHAR seg_trans_pdu[] = {0x01, 0x02, 0x03, 0x04, 0x05, 0x06, 0x07, 0x08,
                                        0x09, 0x0A, 0x0B, 0x0C, 0x0D, 0x0E, 0x0F};

    if(2 != argc)
    {
        printf("Usage: send <destination addr> <Flag[0: Unsegmented, 1: Segmented]>\n");
        return API_FAILURE;
    }

    dst_addr = (UINT16)CLI_strtoi(argv[0], (UINT8)CLI_strlen(argv[0]), 16);
    mode     = (UCHAR)CLI_strtoi(argv[1], (UINT8)CLI_strlen(argv[1]), 16);

    if(MS_FALSE == g_hdr_flag)
    {
        /* Initialize */
        EM_mem_set(&g_hdr, 0x0, sizeof(g_hdr));
        MS_access_cm_get_primary_unicast_address(&g_hdr.saddr);
        g_hdr.daddr = dst_addr;
        g_hdr.ttl   = 0x01;
        g_hdr.ctl   = 0x00;
    }

    /* Update Sequence Number irrespective of g_hdr_flag */
    MS_net_alloc_seq_num(&g_hdr.seq_num);

    /* Set PDU */
    if(0 == mode)
    {
        trans_pdu = (UCHAR *)unseg_trans_pdu;
        len       = sizeof(unseg_trans_pdu);
    }
    else
    {
        trans_pdu = (UCHAR *)seg_trans_pdu;
        len       = sizeof(seg_trans_pdu);
    }

    retval = MS_access_send_pdu(g_hdr.saddr, dst_addr, 0x0000, 0x0000, 2, 0x00E20405, trans_pdu, len, 0);
    printf("retval:%x", retval) return retval;
}

/*********************************************************************
    PUBLIC FUNCTIONS
*/

/**
 * @brief 显示 CLI 帮助信息
 * @description 显示所有可用的 CLI 命令列表和使用说明
 * @param argc 参数个数（未使用）
 * @param argv 参数数组（未使用）
 * @return API_SUCCESS 成功
 * @note 用于查看所有可用的 CLI 命令和其使用方法
 */
API_RESULT cli_demo_help(UINT32 argc, UCHAR *argv[])
{
    MS_IGNORE_UNUSED_PARAM(argc);
    MS_IGNORE_UNUSED_PARAM(argv);
    CLI_help(g_cli_cmd_list, g_cli_cmd_len);
    return API_SUCCESS;
}

/*********************************************************************
*********************************************************************/

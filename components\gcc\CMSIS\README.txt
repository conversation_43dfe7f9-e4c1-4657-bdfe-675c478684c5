* -------------------------------------------------------------------
* Copyright (C) 2011-2013 ARM Limited. All rights reserved.  
* 
* Date:        18 March 2013  
* Revision:    V3.20 
*  
* Project:     Cortex Microcontroller Software Interface Standard (CMSIS)
* Title:       Release Note for CMSIS
*
* -------------------------------------------------------------------


NOTE - Open the index.html file to access CMSIS documentation


The Cortex Microcontroller Software Interface Standard (CMSIS) provides a single standard across all 
Cortex-Mx processor series vendors. It enables code re-use and code sharing across software projects 
and reduces time-to-market for new embedded applications.

CMSIS is released under the terms of the end user license agreement ("CMSIS END USER LICENCE AGREEMENT.pdf").
Any user of the software package is bound to the terms and conditions of the end user license agreement.


You will find the following sub-directories:

Documentation           - Contains CMSIS documentation.
 
DSP_Lib                 - MDK project files, Examples and source files etc.. to build the 
                          CMSIS DSP Software Library for Cortex-M0, Cortex-M3, Cortex-M4 processors.

Include                 - CMSIS Core Support and CMSIS DSP Include Files.

Lib                     - CMSIS DSP Libraries.

RTOS                    - CMSIS RTOS API template header file.

SVD                     - CMSIS SVD Schema files and Conversion Utility.

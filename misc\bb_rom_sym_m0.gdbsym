
_symrom_osal_pwrmgr_powerconserve0 = 0x00014fd9;
P256_ecdh_keygen = 0x00000d03;
P256_ecdh_shared_secret = 0x00000d0b;
_symrom_move_to_slave_function0 = 0x00014085;
_symrom_ll_scheduler0 = 0x00013789;
_symrom_llSetupNextSlaveEvent0 = 0x0000edf9;
_symrom_LL_set_default_conn_params0 = 0x0000828d;
_symrom_bx_to_application = 0x000000d5;
_symrom_P256_mul64 = 0x000001f9;
_symrom_P256_mul128 = 0x0000029d;
_symrom_P256_mulmod = 0x0000032d;
_symrom_P256_sqr64 = 0x00000567;
_symrom_P256_sqr128 = 0x000005c5;
_symrom_P256_sqrmod = 0x00000641;
_symrom_P256_addmod = 0x00000735;
_symrom_P256_submod = 0x00000791;
_symrom_P256_load_1 = 0x000007c3;
_symrom_P256_to_montgomery = 0x000007d7;
_symrom_P256_from_montgomery = 0x00000801;
_symrom_P256_point_is_on_curve = 0x0000081b;
_symrom_P256_greater_or_equal_than = 0x0000086d;
_symrom_P256_negate_mod_m_if = 0x00000887;
_symrom_P256_copy32 = 0x000008a9;
_symrom_P256_copy32_unaligned = 0x000008b5;
_symrom_P256_select = 0x000008c7;
_symrom_P256_double_j = 0x00000927;
_symrom_P256_add_j = 0x0000092b;
_symrom_P256_div2mod = 0x00000983;
_symrom_P256_interpreter = 0x000009e9;
_symrom_P256_sqrmod_many_and_mulmod = 0x00000a4d;
_symrom_P256_modinv = 0x00000a6d;
_symrom_P256_jacobian_to_affine = 0x00000af7;
_symrom_P256_abs_int = 0x00000b33;
_symrom_P256_pointmult = 0x00000b41;
_symrom_P256_ecdh_keygen = 0x00000d03;
_symrom_P256_ecdh_shared_secret = 0x00000d0b;
_symrom___aeabi_uidiv = 0x00000e09;
_symrom___aeabi_uidivmod = 0x00000e09;
_symrom___aeabi_idiv = 0x00000e35;
_symrom___aeabi_idivmod = 0x00000e35;
_symrom___aeabi_memcpy = 0x00000e81;
_symrom___aeabi_memcpy4 = 0x00000e81;
_symrom___aeabi_memcpy8 = 0x00000e81;
_symrom___aeabi_memset = 0x00000ea5;
_symrom___aeabi_memset4 = 0x00000ea5;
_symrom___aeabi_memset8 = 0x00000ea5;
_symrom___aeabi_memclr = 0x00000eb3;
_symrom___aeabi_memclr4 = 0x00000eb3;
_symrom___aeabi_memclr8 = 0x00000eb3;
_symrom_memset = 0x00000eb7;
_symrom_strlen = 0x00000ec9;
_symrom_strcmp = 0x00000ed7;
_symrom_memcmp = 0x00000ef3;
_symrom_strncmp = 0x00000f0d;
_symrom_strtok = 0x00000f2d;
_symrom___aeabi_uread4 = 0x00000f75;
_symrom___rt_uread4 = 0x00000f75;
_symrom__uread4 = 0x00000f75;
_symrom_strtoul = 0x00000f89;
_symrom___rt_ctype_table = 0x00001001;
_symrom__strtoul = 0x00001009;
_symrom_GPIO_IRQHandler = 0x0000112d;
_symrom_HCI_CommandCompleteEvent = 0x00001175;
_symrom_HCI_CommandStatusEvent = 0x000011fd;
_symrom_HCI_DataBufferOverflowEvent = 0x00001251;
_symrom_HCI_DisconnectCmd = 0x0000128d;
_symrom_HCI_EXT_AdvEventNoticeCmd = 0x000012a1;
_symrom_HCI_EXT_BuildRevisionCmd = 0x000012a9;
_symrom_HCI_EXT_ClkDivOnHaltCmd = 0x000012e5;
_symrom_HCI_EXT_ConnEventNoticeCmd = 0x0000130d;
_symrom_HCI_EXT_DeclareNvUsageCmd = 0x00001315;
_symrom_HCI_EXT_DecryptCmd = 0x00001339;
_symrom_HCI_EXT_DelaySleepCmd = 0x0000137d;
_symrom_HCI_EXT_DisconnectImmedCmd = 0x000013a5;
_symrom_HCI_EXT_EnablePTMCmd = 0x000013cd;
_symrom_HCI_EXT_EndModemTestCmd = 0x000013e1;
_symrom_HCI_EXT_HaltDuringRfCmd = 0x00001409;
_symrom_HCI_EXT_ModemHopTestTxCmd = 0x00001431;
_symrom_HCI_EXT_ModemTestRxCmd = 0x00001459;
_symrom_HCI_EXT_ModemTestTxCmd = 0x00001481;
_symrom_HCI_EXT_NumComplPktsLimitCmd = 0x000014a5;
_symrom_HCI_EXT_OnePktPerEvtCmd = 0x000014c9;
_symrom_HCI_EXT_OverlappedProcessingCmd = 0x000014f5;
_symrom_HCI_EXT_PERbyChanCmd = 0x0000151d;
_symrom_HCI_EXT_PacketErrorRateCmd = 0x00001541;
_symrom_HCI_EXT_ResetSystemCmd = 0x00001575;
_symrom_HCI_EXT_SaveFreqTuneCmd = 0x0000159d;
_symrom_HCI_EXT_SetBDADDRCmd = 0x000015c5;
_symrom_HCI_EXT_SetFastTxResponseTimeCmd = 0x00001601;
_symrom_HCI_EXT_SetFreqTuneCmd = 0x00001629;
_symrom_HCI_EXT_SetLocalSupportedFeaturesCmd = 0x00001651;
_symrom_HCI_EXT_SetMaxDtmTxPowerCmd = 0x00001679;
_symrom_HCI_EXT_SetRxGainCmd = 0x000016a1;
_symrom_HCI_EXT_SetSCACmd = 0x000016d1;
_symrom_HCI_EXT_SetSlaveLatencyOverrideCmd = 0x000016f9;
_symrom_HCI_EXT_SetTxPowerCmd = 0x00001721;
_symrom_HCI_ExtTaskRegister = 0x00001751;
_symrom_HCI_GAPTaskRegister = 0x0000175d;
_symrom_HCI_HardwareErrorEvent = 0x00001769;
_symrom_HCI_HostBufferSizeCmd = 0x000017b1;
_symrom_HCI_HostNumCompletedPktCmd = 0x000017e1;
_symrom_HCI_Init = 0x0000183d;
_symrom_HCI_L2CAPTaskRegister = 0x00001879;
_symrom_HCI_LE_AddDevToPeriodicAdvListCmd = 0x00001885;
_symrom_HCI_LE_AddDevToResolvingListCmd = 0x000018a1;
_symrom_HCI_LE_AddWhiteListCmd = 0x000018bd;
_symrom_HCI_LE_ClearAdvSetsCmd = 0x000018e1;
_symrom_HCI_LE_ClearPeriodicAdvListCmd = 0x000018fd;
_symrom_HCI_LE_ClearResolvingListCmd = 0x00001919;
_symrom_HCI_LE_ClearWhiteListCmd = 0x00001935;
_symrom_HCI_LE_ConnUpdateCmd = 0x00001951;
_symrom_HCI_LE_Connection_CTE_Request_EnableCmd = 0x0000196d;
_symrom_HCI_LE_Connection_CTE_Response_EnableCmd = 0x00001999;
_symrom_HCI_LE_ConnectionlessCTE_TransmitEnableCmd = 0x000019c1;
_symrom_HCI_LE_ConnectionlessCTE_TransmitParamCmd = 0x000019dd;
_symrom_HCI_LE_ConnectionlessIQ_SampleEnableCmd = 0x00001a01;
_symrom_HCI_LE_CreateConnCancelCmd = 0x00001a31;
_symrom_HCI_LE_CreateConnCmd = 0x00001a4d;
_symrom_HCI_LE_EncryptCmd = 0x00001a89;
_symrom_HCI_LE_ExtendedCreateConnectionCmd = 0x00001ac1;
_symrom_HCI_LE_LtkReqNegReplyCmd = 0x00001b09;
_symrom_HCI_LE_LtkReqReplyCmd = 0x00001b31;
_symrom_HCI_LE_PeriodicAdvertisingCreateSyncCancelCmd = 0x00001b59;
_symrom_HCI_LE_PeriodicAdvertisingCreateSyncCmd = 0x00001b75;
_symrom_HCI_LE_PeriodicAdvertisingTerminateSyncCmd = 0x00001b9d;
_symrom_HCI_LE_READ_Anatenna_InfoCmd = 0x00001bb9;
_symrom_HCI_LE_RandCmd = 0x00001bd9;
_symrom_HCI_LE_ReadAdvChanTxPowerCmd = 0x00001c09;
_symrom_HCI_LE_ReadBufSizeCmd = 0x00001c29;
_symrom_HCI_LE_ReadChannelMapCmd = 0x00001c4d;
_symrom_HCI_LE_ReadLocalSupportedFeaturesCmd = 0x00001c99;
_symrom_HCI_LE_ReadMaxDataLengthCmd = 0x00001cb9;
//_symrom_HCI_LE_ReadMaximumAdvDataLengthCmd = 0x00001ce9;
//_symrom_HCI_LE_ReadNumberOfSupportAdvSetCmd = 0x00001d11;
_symrom_HCI_LE_ReadPeerResolvableAddressCmd = 0x00001d31;
_symrom_HCI_LE_ReadPeriodicAdvListSizeCmd = 0x00001d51;
_symrom_HCI_LE_ReadPhyMode = 0x00001d71;
_symrom_HCI_LE_ReadRemoteUsedFeaturesCmd = 0x00001db1;
_symrom_HCI_LE_ReadResolvingListSizeCmd = 0x00001dc5;
_symrom_HCI_LE_ReadSuggestedDefaultDataLengthCmd = 0x00001de5;
_symrom_HCI_LE_ReadSupportedStatesCmd = 0x00001e15;
_symrom_HCI_LE_ReadWhiteListSizeCmd = 0x00001e3d;
_symrom_HCI_LE_Read_Rf_Path_CompensationCmd = 0x00001e5d;
_symrom_HCI_LE_Read_Transmit_PowerCmd = 0x00001e7d;
_symrom_HCI_LE_ReceiverTestCmd = 0x00001e9d;
//_symrom_HCI_LE_RemoveAdvSetCmd = 0x00001eb1;
_symrom_HCI_LE_RemovePeriodicAdvListCmd = 0x00001ecd;
_symrom_HCI_LE_RemoveResolvingListCmd = 0x00001ee9;
_symrom_HCI_LE_RemoveWhiteListCmd = 0x00001f0d;
_symrom_HCI_LE_SetAddressResolutionEnableCmd = 0x00001f31;
_symrom_HCI_LE_SetAdvDataCmd = 0x00001f4d;
_symrom_HCI_LE_SetAdvEnableCmd = 0x00001f69;
_symrom_HCI_LE_SetAdvParamCmd = 0x00001f85;
_symrom_HCI_LE_SetDataLengthCmd = 0x00001fb1;
_symrom_HCI_LE_SetDefaultPhyMode = 0x00001fd9;
_symrom_HCI_LE_SetEventMaskCmd = 0x00001ff5;
//_symrom_HCI_LE_SetExtAdvDataCmd = 0x0000202d;
//_symrom_HCI_LE_SetExtAdvEnableCmd = 0x0000204d;
//_symrom_HCI_LE_SetExtAdvParamCmd = 0x0000206d;
//_symrom_HCI_LE_SetExtAdvSetRandomAddressCmd = 0x000020c1;
//_symrom_HCI_LE_SetExtScanRspDataCmd = 0x000020dd;
//_symrom_HCI_LE_SetExtendedScanEnableCmd = 0x000020fd;
//_symrom_HCI_LE_SetExtendedScanParametersCmd = 0x00002119;
_symrom_HCI_LE_SetHostChanClassificationCmd = 0x0000213d;
_symrom_HCI_LE_SetPeriodicAdvDataCmd = 0x00002159;
_symrom_HCI_LE_SetPeriodicAdvEnableCmd = 0x00002175;
_symrom_HCI_LE_SetPeriodicAdvParameterCmd = 0x00002191;
_symrom_HCI_LE_SetPhyMode = 0x000021ad;
//_symrom_HCI_LE_SetRandomAddressCmd = 0x000021c5;
_symrom_HCI_LE_SetResolvablePrivateAddressTimeoutCmd = 0x000021e9;
_symrom_HCI_LE_SetScanEnableCmd = 0x00002219;
_symrom_HCI_LE_SetScanParamCmd = 0x00002235;
_symrom_HCI_LE_SetScanRspDataCmd = 0x00002255;
_symrom_HCI_LE_Set_ConnectionCTE_ReceiveParamCmd = 0x00002271;
_symrom_HCI_LE_Set_ConnectionCTE_TransmitParamCmd = 0x0000229d;
_symrom_HCI_LE_Set_Privacy_ModeCmd = 0x000022c5;
_symrom_HCI_LE_StartEncyptCmd = 0x000022e1;
_symrom_HCI_LE_TestEndCmd = 0x000022f5;
_symrom_HCI_LE_TransmitterTestCmd = 0x0000231d;
_symrom_HCI_LE_WriteSuggestedDefaultDataLengthCmd = 0x00002339;
_symrom_HCI_LE_Write_Rf_Path_CompensationCmd = 0x00002355;
_symrom_HCI_NumOfCompletedPacketsEvent = 0x00002371;
_symrom_HCI_PPLUS_AdvEventDoneNoticeCmd = 0x00002401;
_symrom_HCI_PPLUS_ConnEventDoneNoticeCmd = 0x00002421;
_symrom_HCI_PPLUS_DateLengthChangedNoticeCmd = 0x00002461;
_symrom_HCI_PPLUS_ExtendTRXCmd = 0x000024a1;
_symrom_HCI_PPLUS_PhyUpdateNoticeCmd = 0x000024bd;
_symrom_HCI_ProcessEvent = 0x000024fd;
_symrom_HCI_ReadBDADDRCmd = 0x00002551;
_symrom_HCI_ReadLocalSupportedCommandsCmd = 0x00002571;
_symrom_HCI_ReadLocalSupportedFeaturesCmd = 0x00002589;
_symrom_HCI_ReadLocalVersionInfoCmd = 0x000025ad;
_symrom_HCI_ReadRemoteVersionInfoCmd = 0x000025f5;
_symrom_HCI_ReadRssiCmd = 0x00002625;
_symrom_HCI_ReadTransmitPowerLevelCmd = 0x00002651;
_symrom_HCI_ResetCmd = 0x0000267d;
_symrom_HCI_ReverseBytes = 0x000026a9;
_symrom_HCI_SMPTaskRegister = 0x000026c9;
_symrom_HCI_SendCommandCompleteEvent = 0x000026d5;
_symrom_HCI_SendCommandStatusEvent = 0x0000277d;
_symrom_HCI_SendControllerToHostEvent = 0x0000279d;
_symrom_HCI_SendDataPkt = 0x000027e9;
_symrom_HCI_SetControllerToHostFlowCtrlCmd = 0x00002819;
_symrom_HCI_SetEventMaskCmd = 0x0000285d;
_symrom_HCI_TestAppTaskRegister = 0x0000288d;
_symrom_HCI_ValidConnTimeParams = 0x00002899;
_symrom_HCI_VendorSpecifcCommandCompleteEvent = 0x000028d9;
_symrom_HCI_bm_alloc = 0x000028e9;
_symrom_HardFault_Handler = 0x000028f1;
_symrom_HardFault_IRQHandler = 0x00002909;
_symrom_LL_AddResolvingListLDevice = 0x000029dd;
_symrom_LL_AddWhiteListDevice = 0x00002a95;
_symrom_LL_AdvReportCback = 0x00002b11;
_symrom_LL_CTE_Report_FailedCback = 0x00002ca1;
_symrom_LL_ChanMapUpdate = 0x00002d0d;
//_symrom_LL_ClearAdvSets = 0x00002e2d;
_symrom_LL_ClearResolvingList = 0x00002f41;
_symrom_LL_ClearWhiteList = 0x00002fbd;
_symrom_LL_ConnActive = 0x00003011;
_symrom_LL_ConnParamUpdateCback = 0x00003039;
_symrom_LL_ConnUpdate = 0x000030e5;
_symrom_LL_ConnectionCompleteCback = 0x000031b9;
_symrom_LL_ConnectionIQReportCback = 0x000032d5;
_symrom_LL_Connection_CTE_Request_Enable = 0x000033e5;
_symrom_LL_Connection_CTE_Response_Enable = 0x00003499;
_symrom_LL_ConnectionlessCTE_TransmitEnable = 0x00003505;
_symrom_LL_ConnectionlessCTE_TransmitParam = 0x000035e5;
_symrom_LL_ConnectionlessIQReportCback = 0x000036c1;
_symrom_LL_ConnectionlessIQ_SampleEnable = 0x000037c5;
_symrom_LL_CreateConn = 0x00003901;
_symrom_LL_CreateConn0 = 0x00003949;
_symrom_LL_CreateConnCancel = 0x00003c91;
_symrom_LL_CreateConnCancel0 = 0x00003ca9;
_symrom_LL_CtrlToHostFlowControl = 0x00003d59;
_symrom_LL_DataLengthChangeCback = 0x00003d71;
_symrom_LL_DirectTestEnd = 0x00003e3d;
_symrom_LL_DirectTestTxTest = 0x00003e95;
_symrom_LL_Disconnect = 0x00003eb1;
_symrom_LL_Disconnect0 = 0x00003ec9;
_symrom_LL_DisconnectCback = 0x00003f41;
_symrom_LL_ENC_AES128_Encrypt = 0x00003fc5;
_symrom_LL_ENC_AES128_Encrypt0 = 0x00003fdd;
_symrom_LL_ENC_Decrypt = 0x000040ed;
_symrom_LL_ENC_Decrypt0 = 0x00004105;
_symrom_LL_ENC_Encrypt = 0x00004261;
_symrom_LL_ENC_Encrypt0 = 0x00004278;
_symrom_LL_ENC_GenDeviceIV = 0x000043c1;
_symrom_LL_ENC_GenDeviceSKD = 0x000043f1;
_symrom_LL_ENC_GenerateNonce = 0x00004421;
_symrom_LL_ENC_GeneratePseudoRandNum = 0x00004459;
_symrom_LL_ENC_GenerateTrueRandNum = 0x00004469;
_symrom_LL_ENC_LoadKey = 0x00004489;
_symrom_LL_ENC_ReverseBytes = 0x000044e1;
_symrom_LL_ENC_sm_ah = 0x00004501;
_symrom_LL_EXT_AdvEventNotice = 0x00004543;
_symrom_LL_EXT_BuildRevision = 0x00004547;
_symrom_LL_EXT_ClkDivOnHalt = 0x0000454b;
_symrom_LL_EXT_ConnEventNotice = 0x0000454f;
_symrom_LL_EXT_DeclareNvUsage = 0x00004553;
_symrom_LL_EXT_Decrypt = 0x00004557;
_symrom_LL_EXT_DelaySleep = 0x0000455b;
_symrom_LL_EXT_DisconnectImmed = 0x0000455f;
_symrom_LL_EXT_EndModemTest = 0x00004563;
_symrom_LL_EXT_HaltDuringRf = 0x00004567;
_symrom_LL_EXT_Init_IQ_pBuff = 0x0000456d;
_symrom_LL_EXT_MapPmIoPort = 0x00004581;
_symrom_LL_EXT_ModemHopTestTx = 0x00004585;
_symrom_LL_EXT_ModemTestRx = 0x00004589;
_symrom_LL_EXT_ModemTestTx = 0x0000458d;
_symrom_LL_EXT_NumComplPktsLimit = 0x00004591;
_symrom_LL_EXT_OnePacketPerEvent = 0x000045ad;
_symrom_LL_EXT_OverlappedProcessing = 0x000045b1;
_symrom_LL_EXT_PERbyChan = 0x000045b5;
_symrom_LL_EXT_PacketErrorRate = 0x000045b9;
_symrom_LL_EXT_PacketErrorRateCback = 0x000045bd;
_symrom_LL_EXT_ResetSystem = 0x000045f9;
_symrom_LL_EXT_SaveFreqTune = 0x000045fd;
_symrom_LL_EXT_SetBDADDR = 0x00004601;
_symrom_LL_EXT_SetFastTxResponseTime = 0x00004605;
_symrom_LL_EXT_SetFreqTune = 0x00004609;
_symrom_LL_EXT_SetLocalSupportedFeatures = 0x0000460d;
_symrom_LL_EXT_SetMaxDtmTxPower = 0x00004611;
_symrom_LL_EXT_SetRxGain = 0x00004615;
_symrom_LL_EXT_SetRxGainCback = 0x00004619;
_symrom_LL_EXT_SetSCA = 0x00004635;
_symrom_LL_EXT_SetSlaveLatencyOverride = 0x00004639;
_symrom_LL_EXT_SetTxPower = 0x0000463d;
_symrom_LL_EXT_SetTxPowerCback = 0x00004679;
_symrom_LL_EncChangeCback = 0x00004699;
_symrom_LL_EncKeyRefreshCback = 0x00004715;
_symrom_LL_EncLtkNegReply = 0x0000478d;
_symrom_LL_EncLtkReply = 0x000047d9;
_symrom_LL_EncLtkReqCback = 0x00004831;
_symrom_LL_Encrypt = 0x000048e5;
_symrom_LL_ExtAdvReportCback = 0x00004a8d;
//_symrom_LL_ExtendedCreateConnection = 0x00004bfd;
_symrom_LL_IRQHandler = 0x00004e25;
_symrom_LL_Init = 0x00004eb1;
//_symrom_LL_InitConnectContext = 0x00005045;
_symrom_LL_InitExtendedAdv = 0x0000511d;
_symrom_LL_InitExtendedScan = 0x000051b5;
_symrom_LL_InitPeriodicAdv = 0x000051c5;
_symrom_LL_NumEmptyWlEntries = 0x00005291;
_symrom_LL_PLUS_DisableSlaveLatency = 0x000052a5;
_symrom_LL_PLUS_EnableSlaveLatency = 0x00005435;
_symrom_LL_PLUS_GetAdvDataExtendData = 0x0000548d;
_symrom_LL_PLUS_GetScanRequestExtendData = 0x00005495;
_symrom_LL_PLUS_GetScanerAddr = 0x000054bd;
_symrom_LL_PLUS_PerStasReadByChn = 0x000054d5;
_symrom_LL_PLUS_PerStatsReset = 0x00005519;
_symrom_LL_PLUS_PerStats_Init = 0x00005535;
_symrom_LL_PLUS_SetAdvDataFilterCB = 0x00005545;
_symrom_LL_PLUS_SetScanRequestData = 0x00005551;
_symrom_LL_PLUS_SetScanRequestFilterCB = 0x00005579;
_symrom_LL_PLUS_SetScanRsqData = 0x00005585;
_symrom_LL_PLUS_SetScanRsqDataByIndex = 0x000055b1;
_symrom_LL_PeriodicAdvertisingCreateSync = 0x000055bd;
_symrom_LL_PeriodicAdvertisingCreateSyncCancel = 0x0000560d;
_symrom_LL_PeriodicAdvertisingTerminateSync = 0x00005635;
_symrom_LL_PhyUpdate = 0x00005655;
_symrom_LL_PhyUpdate0 = 0x0000566d;
_symrom_LL_PhyUpdateCompleteCback = 0x00005725;
_symrom_LL_PrdAdvReportCback = 0x000057c9;
_symrom_LL_PrdAdvSyncEstablishedCback = 0x000058b5;
_symrom_LL_PrdAdvSyncLostCback = 0x00005989;
_symrom_LL_ProcessEvent = 0x000059f1;
_symrom_LL_ProcessEvent0 = 0x00005a09;
_symrom_LL_PseudoRand = 0x00005dd9;
_symrom_LL_READ_Anatenna_Info = 0x00005ddd;
_symrom_LL_RX_bm_alloc = 0x00005df1;
_symrom_LL_Rand = 0x00005e05;
_symrom_LL_RandCback = 0x00005e65;
_symrom_LL_ReadAdvChanTxPower = 0x00005e89;
_symrom_LL_ReadBDADDR = 0x00005eb9;
_symrom_LL_ReadCarrSens = 0x00005edd;
_symrom_LL_ReadChanMap = 0x00005ef9;
_symrom_LL_ReadFoff = 0x00005f31;
_symrom_LL_ReadLocalSupportedFeatures = 0x00005f71;
_symrom_LL_ReadLocalVersionInfo = 0x00005f8d;
//_symrom_LL_ReadMaximumAdvDataLength = 0x00005fa5;
//_symrom_LL_ReadNumberOfSupportAdvSet = 0x00005fc5;
_symrom_LL_ReadPeerResolvableAddress = 0x00005fe1;
_symrom_LL_ReadPeriodicAdvListSize = 0x00006005;
_symrom_LL_ReadRemoteUsedFeatures = 0x00006021;
_symrom_LL_ReadRemoteUsedFeaturesCompleteCback = 0x00006061;
_symrom_LL_ReadRemoteVersionInfo = 0x000060d5;
_symrom_LL_ReadRemoteVersionInfoCback = 0x00006131;
_symrom_LL_ReadResolvingListSize = 0x000061a9;
_symrom_LL_ReadRssi = 0x000061b1;
_symrom_LL_ReadSupportedStates = 0x000061e5;
_symrom_LL_ReadTxPowerLevel = 0x00006269;
_symrom_LL_ReadWlSize = 0x000062e5;
_symrom_LL_Read_Rf_Path_Compensation = 0x00006305;
_symrom_LL_Read_Transmit_Power = 0x00006329;
_symrom_LL_RemoveAdvSet = 0x00006335;
_symrom_LL_RemovePeriodicAdvListDevice = 0x00006409;
_symrom_LL_RemoveResolvingListDevice = 0x000064a5;
_symrom_LL_RemoveWhiteListDevice = 0x00006561;
_symrom_LL_RemoveWhiteListDevice0 = 0x00006579;
_symrom_LL_Reset = 0x000065f1;
_symrom_LL_Reset0 = 0x00006609;
_symrom_LL_RxDataCompleteCback = 0x00006791;
_symrom_LL_SetAddressResolutionEnable = 0x00006831;
_symrom_LL_SetAdvControl = 0x00006881;
_symrom_LL_SetAdvControl0 = 0x00006899;
_symrom_LL_SetAdvData = 0x00006a05;
_symrom_LL_SetAdvParam = 0x00006a6d;
_symrom_LL_SetAdvParam0 = 0x00006a9d;
_symrom_LL_SetDataLengh = 0x00006df9;
_symrom_LL_SetDataLengh0 = 0x00006e11;
_symrom_LL_SetDefaultPhyMode = 0x00006ead;
//_symrom_LL_SetExtAdvData = 0x00006edd;
//_symrom_LL_SetExtAdvEnable = 0x00006fc5;
//_symrom_LL_SetExtAdvParam = 0x00007205;
//_symrom_LL_SetExtAdvSetRandomAddress = 0x000073a9;
//_symrom_LL_SetExtScanRspData = 0x000073f5;
//_symrom_LL_SetExtendedScanEnable = 0x00007471;
//_symrom_LL_SetExtendedScanParameters = 0x000074b5;
_symrom_LL_SetPeriodicAdvData = 0x0000751d;
_symrom_LL_SetPeriodicAdvEnable = 0x000075cd;
_symrom_LL_SetPeriodicAdvParameter = 0x00007715;
_symrom_LL_SetPhyMode = 0x000077e1;
_symrom_LL_SetPhyMode0 = 0x000077fd;
_symrom_LL_SetRandomAddress = 0x000078c9;
_symrom_LL_SetResolvablePrivateAddressTimeout = 0x00007929;
_symrom_LL_SetScanControl = 0x00007935;
_symrom_LL_SetScanControl0 = 0x0000794d;
_symrom_LL_SetScanParam = 0x00007a59;
_symrom_LL_SetScanParam0 = 0x00007a75;
_symrom_LL_SetScanRspData = 0x00007b15;
_symrom_LL_SetTxPowerLevel = 0x00007b65;
_symrom_LL_Set_ConnectionCTE_ReceiveParam = 0x00007b91;
_symrom_LL_Set_ConnectionCTE_TransmitParam = 0x00007c99;
_symrom_LL_StartEncrypt = 0x00007e01;
_symrom_LL_TX_bm_alloc = 0x00007f01;
_symrom_LL_TxData = 0x00007f1d;
_symrom_LL_TxData0 = 0x00007f35;
_symrom_LL_WriteSuggestedDefaultDataLength = 0x00007fb9;
_symrom_LL_Write_Rf_Path_Compensation = 0x00007fe5;
_symrom_LL_evt_schedule = 0x00007ff9;
_symrom_LL_master_conn_event0 = 0x000080d5;
_symrom_LL_extAdvTimerExpProcess = 0x000080b5;
_symrom_LL_extInitTimerExpProcess = 0x000080b9;
_symrom_LL_extScanTimerExpProcess = 0x000080bb;
_symrom_LL_master_conn_event = 0x000080bd;
_symrom_LL_prdAdvTimerExpProcess = 0x0000826d;
_symrom_LL_prdScanTimerExpProcess = 0x00008271;
_symrom_LL_set_default_conn_params = 0x00008275;
_symrom_LL_slave_conn_event = 0x000082b9;
_symrom_LL_slave_conn_event0 = 0x000082d1;
_symrom_NMI_Handler = 0x00008481;
_symrom_PendSV_Handler = 0x000084cd;
_symrom_TIM1_IRQHandler = 0x00008545;
_symrom_TIM2_IRQHandler = 0x00008569;
_symrom_WaitRTCCount = 0x00008901;
_symrom___ARM_common_switch8 = 0x00008961;
_symrom__spif_read_status_reg = 0x0000961d;
_symrom__spif_wait_nobusy = 0x00009645;
_symrom_app_sleep_process = 0x00009769;
_symrom_app_wakeup_process = 0x00009779;
_symrom_ate_fun_test = 0x00009789;
_symrom_ate_sleep_process = 0x00009d11;
_symrom_ate_wakeup_process = 0x0000a121;
_symrom_bit_to_byte = 0x0000a1e9;
_symrom_ble_crc24_gen = 0x0000a201;
_symrom_ble_main = 0x0000a2e1;
_symrom_boot_init = 0x0000a361;
_symrom_boot_init0 = 0x0000a379;
_symrom_boot_m0 = 0x0000a3c1;
_symrom_byte_to_bit = 0x0000a535;
_symrom_calculate_whiten_seed = 0x0000a549;
_symrom_clear_timer = 0x0000a5c1;
_symrom_clear_timer_int = 0x0000a5cb;
_symrom_clk_get_pclk = 0x0000a5d1;
_symrom_clk_init = 0x0000a5ed;
_symrom_clk_set_pclk_div = 0x0000a681;
_symrom_clk_spif_ref_clk = 0x0000a6a1;
_symrom_config_RTC = 0x0000a6f9;
_symrom_config_RTC0 = 0x0000a711;
_symrom_rom_crc16 = 0x0000a755;
_symrom_debug_print = 0x0000a791;
_symrom_disableSleep = 0x0000a921;
_symrom_drv_disable_irq = 0x0000a975;
_symrom_drv_enable_irq = 0x0000a99d;
_symrom_drv_irq_init = 0x0000a9c9;
_symrom_dwc_connect = 0x0000a9fd;
_symrom_dwc_data_process = 0x0000aa35;
_symrom_dwc_loop = 0x0000abd1;
_symrom_efuse_read = 0x0000ace1;
_symrom_enableSleep = 0x0000aead;
_symrom_enterSleepProcess = 0x0000aeb9;
_symrom_enterSleepProcess0 = 0x0000aed1;
_symrom_enter_sleep_off_mode = 0x0000afa1;
_symrom_enter_sleep_off_mode0 = 0x0000afb9;
_symrom_getMcuPrecisionCount = 0x0000afe9;
_symrom_getPN23RandNumber = 0x0000aff5;
_symrom_getRxBufferFree = 0x0000b01d;
_symrom_getRxBufferSize = 0x0000b031;
_symrom_getSleepMode = 0x0000b051;
_symrom_getTxBufferFree = 0x0000b05d;
_symrom_getTxBufferSize = 0x0000b071;
_symrom_get_rx_read_ptr = 0x0000b0b1;
_symrom_get_rx_write_ptr = 0x0000b0b9;
_symrom_get_sleep_flag = 0x0000b0c1;
_symrom_get_timer_count = 0x0000b0cd;
_symrom_get_timer_int = 0x0000b0d1;
_symrom_get_tx_read_ptr = 0x0000b0d9;
_symrom_get_tx_write_ptr = 0x0000b0e1;
_symrom_gpio_cfg_analog_io = 0x0000b0e9;
_symrom_gpio_dir = 0x0000b119;
_symrom_gpio_fmux_control = 0x0000b15d;
_symrom_gpio_fmux_set = 0x0000b179;
_symrom_gpio_in_trigger = 0x0000b1b1;
_symrom_gpio_init = 0x0000b219;
_symrom_gpio_interrupt_set = 0x0000b22d;
_symrom_gpio_pull_set = 0x0000b249;
_symrom_gpio_read = 0x0000b291;
_symrom_gpio_wakeup_set = 0x0000b2b5;
_symrom_gpio_write = 0x0000b319;
_symrom_rom_uart_init = 0x0000b379; 
_symrom_hciInitEventMasks = 0x0000b52d;
_symrom_isSleepAllow = 0x0000b555;
_symrom_isTimer1Running = 0x0000b561;
_symrom_isTimer4Running = 0x0000b571;
_symrom_jump_area_init = 0x0000b581;
_symrom_ll24BitTimeCompare = 0x0000b5a9;
_symrom_llAdjSlaveLatencyValue = 0x0000b609;
_symrom_llAllocConnId = 0x0000b629;
_symrom_llAllocateSyncHandle = 0x0000b679;
_symrom_llAtLeastTwoChans = 0x0000b6a9;
_symrom_llCalcMaxScanTime = 0x0000b6ed;
_symrom_llCalcScaFactor = 0x0000b745;
_symrom_llCalcTimerDrift = 0x0000b76d;
_symrom_llCheckForLstoDuringSL = 0x0000b7b1;
_symrom_llCheckWhiteListUsage = 0x0000b7ed;
_symrom_llConnCleanup = 0x0000b809;
_symrom_llConnTerminate = 0x0000b839;
_symrom_llConnTerminate0 = 0x0000b851;
_symrom_llConvertCtrlProcTimeoutToEvent = 0x0000b87d;
_symrom_llConvertLstoToEvent = 0x0000b899;
_symrom_llDeleteSyncHandle = 0x0000b8bd;
_symrom_llDequeueCtrlPkt = 0x0000b8ed;
_symrom_llDequeueDataQ = 0x0000b929;
_symrom_llEnqueueCtrlPkt = 0x0000b953;
_symrom_llEnqueueDataQ = 0x0000b98b;
_symrom_llEqAlreadyValidAddr = 0x0000b9b1;
_symrom_llEqSynchWord = 0x0000b9b5;
_symrom_llEqualBytes = 0x0000b9c9;
_symrom_llEventDelta = 0x0000b9e9;
_symrom_llEventInRange = 0x0000b9fd;
_symrom_llGenerateCRC = 0x0000ba1b;
_symrom_llGenerateValidAccessAddr = 0x0000ba3d;
_symrom_llGetNextAdvChn = 0x0000ba6d;
_symrom_llGetNextAuxAdvChn = 0x0000bac1;
_symrom_llGetNextDataChan = 0x0000bae5;
_symrom_llGetNextDataChanCSA2 = 0x0000bb23;
_symrom_llGtSixConsecZerosOrOnes = 0x0000bb81;
_symrom_llGtTwentyFourTransitions = 0x0000bbb3;
_symrom_llInitFeatureSet = 0x0000bbe5;
_symrom_llInitFeatureSet2MPHY = 0x0000bc1d;
_symrom_llInitFeatureSetCodedPHY = 0x0000bc45;
_symrom_llInitFeatureSetDLE = 0x0000bc6d;
_symrom_llLtTwoChangesInLastSixBits = 0x0000bc89;
_symrom_llMasterEvt_TaskEndOk = 0x0000bcb9;
_symrom_llMemCopyDst = 0x0000be05;
_symrom_llMemCopySrc = 0x0000be1b;
_symrom_llOneBitSynchWordDiffer = 0x0000be35;
_symrom_llPduLengthManagmentReset = 0x0000be4d;
_symrom_llPduLengthUpdate = 0x0000bf01;
_symrom_llPendingUpdateParam = 0x0000c00d;
_symrom_llPhyModeCtrlReset = 0x0000c051;
_symrom_llPhyModeCtrlUpdateNotify = 0x0000c0b9;
_symrom_llPrdAdvDecideNextChn = 0x0000c0f5;
_symrom_llProcessChanMap = 0x0000c185;
_symrom_llProcessMasterControlPacket = 0x0000c1d1;
_symrom_llProcessMasterControlPacket0 = 0x0000c1e9;
_symrom_llProcessMasterControlProcedures = 0x0000c5b9;
_symrom_llProcessMasterControlProcedures0 = 0x0000c5d1;
_symrom_llProcessRxData = 0x0000ca85;
_symrom_llProcessRxData0 = 0x0000ca9d;
_symrom_llProcessSlaveControlPacket = 0x0000cc31;
_symrom_llProcessSlaveControlPacket0 = 0x0000cc49;
_symrom_llProcessSlaveControlProcedures = 0x0000d169;
_symrom_llProcessSlaveControlProcedures0 = 0x0000d181;
_symrom_llProcessTxData = 0x0000d4d1;
_symrom_llProcessTxData0 = 0x0000d4e9;
_symrom_llReleaseAllConnId = 0x0000d55d;
_symrom_llReleaseConnId = 0x0000d561;
_symrom_llReleaseConnId0 = 0x0000d579;
_symrom_llReplaceCtrlPkt = 0x0000d5f5;
_symrom_llResetConnId = 0x0000d60d;
_symrom_llResetRfCounters = 0x0000d6ed;
_symrom_llSecAdvAllow = 0x0000d701;
_symrom_llSetNextDataChan = 0x0000d769;
_symrom_llSetNextPhyMode = 0x0000d839;
_symrom_llSetupAdv = 0x0000d8a9;
_symrom_llSetupAdv0 = 0x0000d8c1;
_symrom_llSetupAdvExtIndPDU = 0x0000d949;
_symrom_llSetupAuxAdvIndPDU = 0x0000db4d;
_symrom_llSetupAuxChainIndPDU = 0x0000dda1;
_symrom_llSetupAuxConnectReqPDU = 0x0000df91;
_symrom_llSetupAuxConnectRspPDU = 0x0000e025;
_symrom_llSetupAuxScanRspPDU = 0x0000e09d;
_symrom_llSetupAuxSyncIndPDU = 0x0000e109;
_symrom_llSetupAuxSyncIndPDU0 = 0x0000e121;
_symrom_llSetupCTEReq = 0x0000e2c1;
_symrom_llSetupCTERsp = 0x0000e38d;
_symrom_llSetupConn = 0x0000e457;
_symrom_llSetupDataLenghtReq = 0x0000e459;
_symrom_llSetupDataLenghtRsp = 0x0000e4d5;
_symrom_llSetupDirectedAdvEvt = 0x0000e551;
_symrom_llSetupEncReq = 0x0000e6a1;
_symrom_llSetupEncRsp = 0x0000e725;
_symrom_llSetupExtAdvEvent = 0x0000e7b1;
_symrom_llSetupExtInit = 0x0000eb09;
_symrom_llSetupExtScan = 0x0000eb81;
_symrom_llSetupFeatureSetReq = 0x0000ec01;
_symrom_llSetupFeatureSetRsp = 0x0000ec63;
_symrom_llSetupInit = 0x0000ecc1;
_symrom_llSetupNextMasterEvent = 0x0000ed39;
_symrom_llSetupNextSlaveEvent = 0x0000ede1;
_symrom_llSetupNonConnectableAdvEvt = 0x0000ef65;
_symrom_llSetupPauseEncReq = 0x0000f075;
_symrom_llSetupPauseEncRsp = 0x0000f0c5;
_symrom_llSetupPhyReq = 0x0000f121;
_symrom_llSetupPhyRsp = 0x0000f177;
_symrom_llSetupPhyUpdateInd = 0x0000f1cd;
_symrom_llSetupPrdAdvEvent = 0x0000f239;
_symrom_llSetupPrdScan = 0x0000f3b9;
_symrom_llSetupRejectExtInd = 0x0000f44d;
_symrom_llSetupRejectInd = 0x0000f479;
_symrom_llSetupScan = 0x0000f4a5;
_symrom_llSetupScan0 = 0x0000f4bd;
_symrom_llSetupScanInit = 0x0000f54d;
_symrom_llSetupScannableAdvEvt = 0x0000f55d;
_symrom_llSetupSecAdvEvt = 0x0000f66d;
_symrom_llSetupSecConnectableAdvEvt = 0x0000f6e9;
_symrom_llSetupSecInit = 0x0000f7c1;
_symrom_llSetupSecNonConnectableAdvEvt = 0x0000f875;
_symrom_llSetupSecScan = 0x0000f94d;
_symrom_llSetupSecScannableAdvEvt = 0x0000fa19;
_symrom_llSetupStartEncReq = 0x0000faf1;
_symrom_llSetupStartEncRsp = 0x0000fb15;
_symrom_llSetupSyncInfo = 0x0000fb59;
_symrom_llSetupTermInd = 0x0000fc2d;
_symrom_llSetupUndirectedAdvEvt = 0x0000fc91;
_symrom_llSetupUnknownRsp = 0x0000fda5;
_symrom_llSetupUpdateChanReq = 0x0000fdf9;
_symrom_llSetupUpdateParamReq = 0x0000fe6d;
_symrom_llSetupVersionIndReq = 0x0000ff05;
_symrom_llSlaveEvt_TaskAbort = 0x0000ff79;
_symrom_llSlaveEvt_TaskEndOk = 0x0000ff95;
_symrom_llSlaveEvt_TaskEndOk0 = 0x0000ffad;
_symrom_llTrxNumAdaptiveConfig = 0x00010181;
_symrom_llValidAccessAddr = 0x0001019b;
_symrom_llWaitUs = 0x000101e1;
_symrom_llWriteTxData = 0x00010209;
_symrom_ll_CalcRandomAddr = 0x0001028f;
_symrom_ll_ResolveRandomAddrs = 0x000102cd;
_symrom_ll_addTask = 0x00010315;
_symrom_ll_add_adv_task = 0x00010445;
_symrom_ll_add_adv_task_periodic = 0x00010461;
_symrom_ll_adptive_adj_next_time = 0x0001047d;
_symrom_ll_adptive_smart_window = 0x000104fd;
_symrom_ll_adv_scheduler = 0x000105ad;
_symrom_ll_adv_scheduler_periodic = 0x000105c9;
_symrom_ll_allocAuxAdvTimeSlot = 0x000105e5;
_symrom_ll_allocAuxAdvTimeSlot_prd = 0x00010679;
_symrom_ll_debug_output = 0x00010719;
_symrom_ll_deleteTask = 0x00010731;
_symrom_ll_delete_adv_task = 0x00010765;
_symrom_ll_delete_adv_task_periodic = 0x00010781;
_symrom_ll_ext_adv_schedule_next_event = 0x0001079d;
_symrom_ll_ext_init_schedule_next_event = 0x000107c1;
_symrom_ll_ext_scan_schedule_next_event = 0x000107dd;
_symrom_ll_generateExtAdvDid = 0x000107f9;
_symrom_ll_generateTxBuffer = 0x00010801;
_symrom_ll_getFirstAdvChn = 0x000109c1;
_symrom_ll_getRPAListEntry = 0x000109cd;
_symrom_ll_get_next_active_conn = 0x00010a39;
_symrom_ll_get_next_timer = 0x00010aa1;
_symrom_ll_hw_clr_irq = 0x00010add;
_symrom_ll_hw_config = 0x00010aed;
_symrom_ll_hw_get_anchor = 0x00010b6d;
_symrom_ll_hw_get_fsm_status = 0x00010b79;
_symrom_ll_hw_get_iq_RawSample = 0x00010b89;
_symrom_ll_hw_get_irq_status = 0x00010bbd;
_symrom_ll_hw_get_last_ack = 0x00010bcd;
_symrom_ll_hw_get_loop_cycle = 0x00010be9;
_symrom_ll_hw_get_loop_time = 0x00010bf5;
_symrom_ll_hw_get_nAck = 0x00010c01;
_symrom_ll_hw_get_rfifo_depth = 0x00010c11;
_symrom_ll_hw_get_rfifo_info = 0x00010c25;
_symrom_ll_hw_get_rxPkt_CrcErr_num = 0x00010c45;
_symrom_ll_hw_get_rxPkt_CrcOk_num = 0x00010c55;
_symrom_ll_hw_get_rxPkt_Total_num = 0x00010c69;
_symrom_ll_hw_get_rxPkt_num = 0x00010c79;
_symrom_ll_hw_get_rxPkt_stats = 0x00010c85;
_symrom_ll_hw_get_snNesn = 0x00010c9d;
_symrom_ll_hw_get_tfifo_info = 0x00010cad;
_symrom_ll_hw_get_tfifo_wrptr = 0x00010ccd;
_symrom_ll_hw_get_tr_mode = 0x00010cdd;
_symrom_ll_hw_get_txAck = 0x00010ced;
_symrom_ll_hw_go = 0x00010cf9;
_symrom_ll_hw_ign_rfifo = 0x00010df9;
_symrom_ll_hw_process_RTO = 0x00010e05;
_symrom_ll_hw_read_rfifo = 0x00010e6d;
_symrom_ll_hw_read_rfifo_pplus = 0x00010ee9;
_symrom_ll_hw_read_rfifo_zb = 0x00010f51;
_symrom_ll_hw_read_tfifo_packet = 0x00010fad;
_symrom_ll_hw_read_tfifo_rtlp = 0x00010ff5;
_symrom_ll_hw_read_tfifo_rtlp0 = 0x0001100d;
_symrom_ll_hw_rst_rfifo = 0x000110b1;
_symrom_ll_hw_rst_tfifo = 0x000110e9;
_symrom_ll_hw_set_ant_pattern = 0x000110f5;
_symrom_ll_hw_set_ant_switch_mode = 0x00011101;
_symrom_ll_hw_set_ant_switch_timing = 0x00011115;
_symrom_ll_hw_set_crc_fmt = 0x0001112d;
_symrom_ll_hw_set_cte_rxSupp = 0x0001113d;
_symrom_ll_hw_set_cte_txSupp = 0x00011155;
_symrom_ll_hw_set_empty_head = 0x00011169;
_symrom_ll_hw_set_irq = 0x00011175;
_symrom_ll_hw_set_loop_nack_num = 0x00011181;
_symrom_ll_hw_set_loop_timeout = 0x0001118d;
_symrom_ll_hw_set_pplus_pktfmt = 0x000111a1;
_symrom_ll_hw_set_rtlp = 0x000111cd;
_symrom_ll_hw_set_rtlp_1st = 0x0001121d;
_symrom_ll_hw_set_rtx = 0x00011265;
_symrom_ll_hw_set_rx_timeout = 0x00011279;
_symrom_ll_hw_set_rx_timeout_1st = 0x00011285;
_symrom_ll_hw_set_rx_tx_interval = 0x00011291;
_symrom_ll_hw_set_srx = 0x000112a5;
_symrom_ll_hw_set_stx = 0x000112b9;
_symrom_ll_hw_set_tfifo_space = 0x000112cd;
_symrom_ll_hw_set_timing = 0x000112e5;
_symrom_ll_hw_set_trlp = 0x00011381;
_symrom_ll_hw_set_trx = 0x000113c9;
_symrom_ll_hw_set_trx_settle = 0x000113dd;
_symrom_ll_hw_set_tx_rx_interval = 0x000113f1;
_symrom_ll_hw_set_tx_rx_release = 0x00011405;
_symrom_ll_hw_trigger = 0x00011421;
_symrom_ll_hw_trx_settle_config = 0x00011445;
_symrom_ll_hw_tx2rx_timing_config = 0x00011489;
_symrom_ll_hw_update = 0x000114dd;
_symrom_ll_hw_update_rtlp_mode = 0x00011539;
_symrom_ll_hw_update_trlp_mode = 0x00011579;
_symrom_ll_hw_write_tfifo = 0x000115c1;
_symrom_ll_isAddrInWhiteList = 0x00011649;
_symrom_ll_isFirstAdvChn = 0x000116a9;
_symrom_ll_isIrkAllZero = 0x000116c7;
_symrom_ll_isLegacyAdv = 0x000116dd;
_symrom_ll_parseExtHeader = 0x000116ed;
_symrom_ll_prd_adv_schedule_next_event = 0x000117a9;
_symrom_ll_prd_scan_schedule_next_event = 0x000117cd;
_symrom_ll_processBasicIRQ = 0x000117e9;
_symrom_ll_processExtAdvIRQ = 0x00013401;
_symrom_ll_processExtInitIRQ = 0x00013405;
_symrom_ll_processExtScanIRQ = 0x00013409;
_symrom_ll_processMissMasterEvt = 0x0001340d;
_symrom_ll_processMissSlaveEvt = 0x000134ed;
_symrom_ll_processPrdAdvIRQ = 0x000135f5;
_symrom_ll_processPrdScanIRQ = 0x000135f9;
_symrom_ll_readLocalIRK = 0x000135fd;
_symrom_ll_readPeerIRK = 0x00013661;
_symrom_ll_read_rxfifo = 0x000136c5;
_symrom_ll_read_rxfifo0 = 0x000136cd;
_symrom_ll_schedule_next_event = 0x00013761;
_symrom_ll_scheduler = 0x00013771;
_symrom_ll_updateAuxAdvTimeSlot = 0x00013a11;
_symrom_ll_updateExtAdvRemainderTime = 0x00013a39;
_symrom_log_clr_putc = 0x00013ab9;
_symrom_log_debug_level = 0x00013ac5;
_symrom_log_get_debug_level = 0x00013ad9;
_symrom_log_printf = 0x00013ae5;
_symrom_log_set_putc = 0x00013b05;
_symrom_log_vsprintf = 0x00013b11;
_symrom_move_to_master_function = 0x00013f09;
_symrom_move_to_master_function0 = 0x00013f21;
_symrom_move_to_slave_function = 0x0001406d;
_symrom_move_to_slave_function0 = 0x00014085;
_symrom_osalAddTimer = 0x00014439;
_symrom_osalDeleteTimer = 0x000144a9;
_symrom_osalFindTimer = 0x000144b5;
_symrom_osalTimeUpdate = 0x000144d5;
_symrom_osalTimeUpdate1 = 0x00014541;
_symrom_osalTimerInit = 0x0001457d;
_symrom_osalTimerUpdate = 0x00014589;
_symrom_osal_CbTimerInit = 0x00014621;
_symrom_osal_CbTimerProcessEvent = 0x00014641;
_symrom_osal_CbTimerStart = 0x000146a9;
_symrom_osal_CbTimerStop = 0x00014711;
_symrom_osal_CbTimerUpdate = 0x00014751;
_symrom_osal_ConvertUTCSecs = 0x000147a1;
_symrom_osal_ConvertUTCTime = 0x00014841;
_symrom_osal_GetSystemClock = 0x00014949;
_symrom_osal_bm_adjust_header = 0x00014955;
_symrom_osal_bm_adjust_tail = 0x0001497d;
_symrom_osal_bm_alloc = 0x000149a9;
_symrom_osal_bm_free = 0x000149d9;
_symrom_osal_buffer_uint24 = 0x00014a21;
_symrom_osal_buffer_uint32 = 0x00014a2f;
_symrom_osal_build_uint16 = 0x00014a41;
_symrom_osal_build_uint32 = 0x00014a4d;
_symrom_osal_clear_event = 0x00014a89;
_symrom_osal_getClock = 0x00014abd;
_symrom_osal_get_timeoutEx = 0x00014ac9;
_symrom_osal_init_system = 0x00014aed;
_symrom_osal_isbufset = 0x00014b1d;
_symrom_osal_mem_alloc = 0x00014b3d;
_symrom_osal_mem_free = 0x00014c01;
_symrom_osal_mem_init = 0x00014c25;
_symrom_osal_mem_kick = 0x00014c8d;
_symrom_osal_mem_set_heap = 0x00014cb5;
_symrom_osal_memcmp = 0x00014ccd;
_symrom_osal_memcpy = 0x00014ce9;
_symrom_osal_memdup = 0x00014cf9;
_symrom_osal_memset = 0x00014d15;
_symrom_osal_msg_allocate = 0x00014d1d;
_symrom_osal_msg_deallocate = 0x00014d43;
_symrom_osal_msg_dequeue = 0x00014d65;
_symrom_osal_msg_enqueue = 0x00014d91;
_symrom_osal_msg_enqueue_max = 0x00014dc3;
_symrom_osal_msg_extract = 0x00014e6d;
_symrom_osal_msg_find = 0x00014e9d;
_symrom_osal_msg_push = 0x00014ed1;
_symrom_osal_msg_push_front = 0x00014eeb;
_symrom_osal_msg_receive = 0x00014ef5;
_symrom_osal_msg_send = 0x00014f59;
_symrom_osal_next_timeout = 0x00014f7d;
_symrom_osal_pwrmgr_device = 0x00014fa5;
_symrom_osal_pwrmgr_init = 0x00014fb1;
_symrom_osal_pwrmgr_powerconserve = 0x00014fc1;
_symrom_osal_pwrmgr_task_state = 0x000150f9;
_symrom_osal_rand = 0x00015129;
_symrom_osal_revmemcpy = 0x00015145;
_symrom_osal_run_system = 0x00015159;
_symrom_osal_self = 0x000151f5;
_symrom_osal_setClock = 0x00015201;
_symrom_osal_set_event = 0x0001520d;
_symrom_osal_start_reload_timer = 0x00015259;
_symrom_osal_start_system = 0x00015285;
_symrom_osal_start_timerEx = 0x0001528b;
_symrom_osal_stop_timerEx = 0x000152b3;
_symrom_osal_strlen = 0x000152dd;
_symrom_osal_timer_num_active = 0x000152e5;
_symrom_phy_sec_app_key = 0x00015315;
_symrom_phy_sec_decrypt = 0x0001531d;
_symrom_phy_sec_efuse_lock = 0x0001532d;
_symrom_phy_sec_encrypt = 0x00015339;
_symrom_phy_sec_init = 0x00015349;
_symrom_phy_sec_key_valid = 0x0001540d;
_symrom_prog_process_data = 0x00015b19;
_symrom_prog_uart_command = 0x00015c51;
_symrom_prog_uart_fct_command = 0x00015c71;
_symrom_prog_uart_handle = 0x00015c8d;
_symrom_read_LL_remainder_time = 0x00015cbd;
_symrom_read_current_fine_time = 0x00015cc9;
_symrom_read_ll_adv_remainder_time = 0x00015cf1;
_symrom_reset_conn_buf = 0x00015cfd;
_symrom_rf_phy_change_cfg = 0x00016085;
_symrom_rom_board_init = 0x00016a09;
_symrom_rtc_clear = 0x00016ab5;
_symrom_rtc_config_prescale = 0x00016ad1;
_symrom_rtc_get_counter = 0x00016b15;
_symrom_rtc_start = 0x00016b25;
_symrom_rtc_stop = 0x00016b35;
_symrom_setSleepMode = 0x00016b45;
_symrom_set_access_address = 0x00016b51;
_symrom_set_channel = 0x00016b5d;
_symrom_set_crc_seed = 0x00016b9d;
_symrom_set_gpio_pull_down_ate = 0x00016bb5;
_symrom_set_gpio_pull_up_ate = 0x00016bcb;
_symrom_set_int = 0x00016be1;
_symrom_set_max_length = 0x00016bed;
_symrom_set_sleep_flag = 0x00016c01;
_symrom_set_timer = 0x00016c2d;
_symrom_set_whiten_seed = 0x00016cc9;
_symrom_spif_cmd = 0x00016d49;
_symrom_spif_config = 0x00016dc5;
_symrom_spif_erase_all = 0x00016ea1;
_symrom_spif_erase_block64 = 0x00016ed1;
_symrom_spif_erase_chip = 0x00016f55;
_symrom_spif_erase_sector = 0x00016fa9;
_symrom_spif_flash_size = 0x00017029;
_symrom_spif_flash_status_reg_0 = 0x0001703d;
_symrom_spif_flash_status_reg_1 = 0x00017047;
_symrom_spif_init = 0x00017051;
_symrom_spif_rddata = 0x0001713d;
_symrom_spif_read = 0x00017165;
_symrom_spif_read_dma = 0x0001717d;
_symrom_spif_read_id = 0x00017209;
_symrom_spif_release_deep_sleep = 0x000172cd;
_symrom_spif_set_deep_sleep = 0x00017349;
_symrom_spif_wrdata = 0x0001736d;
_symrom_spif_write = 0x00017395;
_symrom_spif_write_dma = 0x0001744d;
_symrom_spif_write_protect = 0x000174f9;
_symrom_sram_ret_patch = 0x00017591;
_symrom_update_rx_read_ptr = 0x00017609;
_symrom_update_rx_write_ptr = 0x00017635;
_symrom_update_tx_read_ptr = 0x00017659;
_symrom_update_tx_write_ptr = 0x00017685;
_symrom_wakeupProcess = 0x000176a9;
_symrom_wakeupProcess0 = 0x000176c5;
_symrom_wakeup_init = 0x000178a5;
_symrom_wakeup_init0 = 0x000178bd;
_symrom_zigbee_crc16_gen = 0x0001798d;
_symrom_g_hclk_table = 0x00017be0;
_symrom_supportedCmdsTable = 0x00017c00;
_symrom_hciCmdTable = 0x00017c44;
_symrom_SCA = 0x00017c4c;
_symrom_hclk_per_us = 0x1fff0818;
_symrom_hclk_per_us_shift = 0x1fff081c;
_symrom_s_prog_time_save = 0x1fff0828;
_symrom_s_prog_timeout = 0x1fff082c;
_symrom_DFL_ENTRY_BASE = 0x1fff0830;
_symrom_receive_timeout_flag = 0x1fff0850;
_symrom_osal_sys_tick = 0x1fff0860;
_symrom_g_timer4_irq_pending_time = 0x1fff0864;
_symrom_g_hclk = 0x1fff0874;
_symrom_m_in_critical_region = 0x1fff0878;
_symrom_s_rom_debug_level = 0x1fff0888;
_symrom_s_spif_ctx = 0x1fff0894;
_symrom_osal_qHead = 0x1fff08b8;
_symrom_baseTaskID = 0x1fff08c0;
_symrom_OSAL_timeSeconds = 0x1fff08cc;
_symrom_osalMemStat = 0x1fff08d0;
_symrom_theHeap = 0x1fff08d4;
_symrom_ff1 = 0x1fff08d8;
_symrom_heapSize = 0x1fff08dc;
_symrom_ll_remain_time = 0x1fff08e4;
_symrom_pwrmgr_attribute = 0x1fff08e8;
_symrom_timerHead = 0x1fff08f0;
_symrom_hciPTMenabled = 0x1fff08f8;
_symrom_ctrlToHostEnable = 0x1fff08f9;
_symrom_numHostBufs = 0x1fff08fa;
_symrom_hciCtrlCmdToken = 0x1fff08fc;
_symrom_bleEvtMask = 0x1fff0900;
_symrom_pHciEvtMask = 0x1fff0904;
_symrom_hciTaskID = 0x1fff090c;
_symrom_hciTestTaskID = 0x1fff090d;
_symrom_g_llPrdAdvDeviceNum = 0x1fff092a;
_symrom_hciGapTaskID = 0x1fff090e;
_symrom_hciL2capTaskID = 0x1fff090f;
_symrom_hciSmpTaskID = 0x1fff0910;
_symrom_hciExtTaskID = 0x1fff0911;
_symrom_g_maxConnNum = 0x1fff0914;
_symrom_g_maxPktPerEventTx = 0x1fff0915;
_symrom_g_maxPktPerEventRx = 0x1fff0916;
_symrom_g_blePktVersion = 0x1fff0917;
_symrom_g_llRlEnable = 0x1fff0918;
_symrom_g_llScanMode = 0x1fff0919;
_symrom_g_llAdvMode = 0x1fff091a;
_symrom_LL_TaskID = 0x1fff091b;
_symrom_llState = 0x1fff091c;
_symrom_numComplPkts = 0x1fff091d;
_symrom_numComplPktsLimit = 0x1fff091e;
_symrom_fastTxRespTime = 0x1fff091f;
_symrom_g_llWlDeviceNum = 0x1fff0920;
_symrom_g_llRlDeviceNum = 0x1fff0921;
_symrom_rxFifoFlowCtrl = 0x1fff0922;
_symrom_llSecondaryState = 0x1fff0923;
_symrom_g_extAdvNumber = 0x1fff0924;
_symrom_g_perioAdvNumber = 0x1fff0925;
_symrom_g_schExtAdvNum = 0x1fff0926;
_symrom_g_currentExtAdv = 0x1fff0927;
_symrom_g_schExtAdvNum_periodic = 0x1fff0928;
_symrom_g_currentExtAdv_periodic = 0x1fff0929;
_symrom_g_llPrdAdvDeviceNum = 0x1fff092a;
_symrom_g_llRlTimeout = 0x1fff092c;
_symrom_g_advSetMaximumLen = 0x1fff0930;
_symrom_conn_param = 0x1fff0934;
_symrom_g_pExtendedAdvInfo = 0x1fff0938;
_symrom_g_pPeriodicAdvInfo = 0x1fff093c;
_symrom_g_pLLcteISample = 0x1fff0940;
_symrom_g_pLLcteQSample = 0x1fff0944;
_symrom_g_llHdcDirAdvTime = 0x1fff0948;
_symrom_g_pAdvSchInfo = 0x1fff094c;
_symrom_g_advPerSlotTick = 0x1fff0950;
_symrom_g_advSlotPeriodic = 0x1fff0954;
_symrom_g_pAdvSchInfo_periodic = 0x1fff0958;
_symrom_g_timerExpiryTick = 0x1fff095c;
_symrom_chanMapUpdate = 0x1fff0960;
_symrom_ownPublicAddr = 0x1fff0965;
_symrom_ownRandomAddr = 0x1fff096b;
_symrom_verInfo = 0x1fff0972;
_symrom_peerInfo = 0x1fff0978;
_symrom_g_currentAdvTimer = 0x1fff0980;
_symrom_g_currentTimerTask = 0x1fff0984;
_symrom_g_adv_taskID = 0x1fff0988;
_symrom_g_conn_taskID = 0x1fff0989;
_symrom_g_smartWindowRTOCnt = 0x1fff098a;
_symrom_isPeerRpaStore = 0x1fff098b;
_symrom_g_same_rf_channel_flag = 0x1fff098c;
_symrom_g_currentPeerAddrType = 0x1fff098d;
_symrom_g_currentLocalAddrType = 0x1fff098e;
_symrom_storeRpaListIndex = 0x1fff098f;
_symrom_llTaskState = 0x1fff0990;
_symrom_g_adv_taskEvent = 0x1fff0992;
_symrom_g_conn_taskEvent = 0x1fff0994;
_symrom_llWaitingIrq = 0x1fff0998;
_symrom_ISR_entry_time = 0x1fff099c;
_symrom_slave_conn_event_recv_delay = 0x1fff09a0;
_symrom_g_smartWindowLater = 0x1fff09a4;
_symrom_g_smartWindowActiveCnt = 0x1fff09a8;
_symrom_g_smartWindowPreAnchPoint = 0x1fff09ac;
_symrom_g_getPn23_cnt = 0x1fff09b0;
_symrom_g_getPn23_seed = 0x1fff09b4;
_symrom_llScanTime = 0x1fff09b8;
_symrom_p_perStatsByChan = 0x1fff09bc;
_symrom_LL_PLUS_AdvDataFilterCBack = 0x1fff09c0;
_symrom_LL_PLUS_ScanRequestFilterCBack = 0x1fff09c4;
_symrom_g_new_master_delta = 0x1fff09c8;
_symrom_llScanT1 = 0x1fff09cc;
_symrom_llCurrentScanChn = 0x1fff09d0;
_symrom_g_currentLocalRpa = 0x1fff09d4;
_symrom_g_currentPeerRpa = 0x1fff09da;
_symrom_currentPeerRpa = 0x1fff09e0;
_symrom_g_dle_taskID = 0x1fff09e6;
_symrom_g_dle_taskEvent = 0x1fff09e8;
_symrom_g_phyChg_taskID = 0x1fff09ea;
_symrom_g_phyChg_taskEvent = 0x1fff09ec;
_symrom_g_smartWindowSize = 0x1fff09f0;
_symrom_g_smartWindowSizeNew = 0x1fff09f4;
_symrom_g_smartWindowActive = 0x1fff09f8;
_symrom_g_interAuxPduDuration = 0x1fff09fc;
_symrom_g_rfTxPathCompensation = 0x1fff0a00;
_symrom_g_rfRxPathCompensation = 0x1fff0a02;
_symrom_connUpdateTimer = 0x1fff0a04;
_symrom_sleep_flag = 0x1fff0a0c;
_symrom_g_wakeup_rtc_tick = 0x1fff0a10;
_symrom_g_counter_traking_avg = 0x1fff0a14;
_symrom_g_TIM2_IRQ_TIM3_CurrCount = 0x1fff0a18;
_symrom_g_TIM2_IRQ_to_Sleep_DeltTick = 0x1fff0a1c;
_symrom_g_TIM2_IRQ_PendingTick = 0x1fff0a20;
_symrom_g_osal_tick_trim = 0x1fff0a24;
_symrom_g_osalTickTrim_mod = 0x1fff0a28;
_symrom_rtc_mod_value = 0x1fff0a2c;
_symrom_g_counter_traking_cnt = 0x1fff0a30;
_symrom_sleep_tick = 0x1fff0a34;
_symrom_counter_tracking = 0x1fff0a38;
_symrom_forever_write = 0x1fff0a3c;
_symrom_g_TIM2_wakeup_delay = 0x1fff0a40;
_symrom_g_rfPhyTpCal0 = 0x1fff0a44;
_symrom_g_rfPhyTpCal1 = 0x1fff0a45;
_symrom_g_rfPhyTpCal0_2Mbps = 0x1fff0a46;
_symrom_g_rfPhyTpCal1_2Mbps = 0x1fff0a47;
_symrom_g_rfPhyTxPower = 0x1fff0a48;
_symrom_g_rfPhyPktFmt = 0x1fff0a49;
_symrom_g_system_clk = 0x1fff0a4a;
_symrom_g_rfPhyClkSel = 0x1fff0a4b;
_symrom_g_rxAdcClkSel = 0x1fff0a4c;
_symrom_g_dtmModeType = 0x1fff0a4d;
_symrom_g_dtmLength = 0x1fff0a4e;
_symrom_g_dtmExtLen = 0x1fff0a4f;
_symrom_g_dtmPKT = 0x1fff0a50;
_symrom_g_dtmTxPower = 0x1fff0a51;
_symrom_g_dtmRssi = 0x1fff0a52;
_symrom_g_dtmCarrSens = 0x1fff0a53;
_symrom_g_dtmPktIntv = 0x1fff0a54;
_symrom_g_dtmPktCount = 0x1fff0a56;
_symrom_g_dtmRxCrcNum = 0x1fff0a58;
_symrom_g_dtmRxTONum = 0x1fff0a5a;
_symrom_g_dtmRsp = 0x1fff0a5c;
_symrom_g_dtmFoff = 0x1fff0a5e;
_symrom_g_rfPhyRxDcIQ = 0x1fff0a60;
_symrom_g_dtmTick = 0x1fff0a64;
_symrom_g_rfPhyFreqOffSet = 0x1fff0a68;
_symrom_g_rfPhyDtmCmd = 0x1fff0a69;
_symrom_g_rfPhyDtmEvt = 0x1fff0a6b;
_symrom_g_dtmCmd = 0x1fff0a6d;
_symrom_g_dtmFreq = 0x1fff0a6e;
_symrom_g_dtmCtrl = 0x1fff0a6f;
_symrom_g_dtmPara = 0x1fff0a70;
_symrom_g_dtmEvt = 0x1fff0a71;
_symrom_g_dtmStatus = 0x1fff0a72;
_symrom_g_dtmTpCalEnable = 0x1fff0a73;
_symrom_g_dtmPerAutoIntv = 0x1fff0a74;
_symrom_g_dtmAccessCode = 0x1fff0a78;
_symrom_g_system_reset_cause = 0x1fff0a80;
_symrom_cbTimers = 0x1fff0afc;
_symrom_g_llSleepContext = 0x1fff0b74;
_symrom_syncInfo = 0x1fff0b84;
_symrom_scanSyncInfo = 0x1fff0b96;
_symrom_adv_param = 0x1fff0ba6;
_symrom_scanInfo = 0x1fff0bbc;
_symrom_initInfo = 0x1fff0bd4;
_symrom_extScanInfo = 0x1fff0be8;
_symrom_extInitInfo = 0x1fff0c10;
_symrom_g_llPeriodAdvSyncInfo = 0x1fff0c50;
_symrom_g_ll_conn_ctx = 0x1fff0d30;
_symrom_deviceFeatureSet = 0x1fff0e48;
_symrom_g_llWhitelist = 0x1fff0e51;
_symrom_g_llResolvinglist = 0x1fff0e89;
_symrom_g_pmCounters = 0x1fff0ffc;
_symrom_g_llPduLen = 0x1fff1084;
_symrom_g_llPeriodicAdvlist = 0x1fff10a0;
_symrom_rfCounters = 0x1fff10e0;
_symrom_ext_adv_hdr = 0x1fff10ec;
_symrom_dataPkt = 0x1fff1118;
_symrom_cachedTRNGdata = 0x1fff1138;
_symrom_whiten_seed = 0x1fff1144;
_symrom_g_tx_adv_buf = 0x1fff116c;
_symrom_g_tx_ext_adv_buf = 0x1fff1278;
_symrom_tx_scanRsp_desc = 0x1fff1384;
_symrom_g_rx_adv_buf = 0x1fff1490;

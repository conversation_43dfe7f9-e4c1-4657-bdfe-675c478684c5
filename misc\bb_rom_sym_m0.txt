#<SYMDEFS># ARM Linker, 5050041: Last Updated: Tue Jun 16 11:44:23 2020
0x000000d5 T bx_to_application
0x000001f9 T P256_mul64
0x0000029d T P256_mul128
0x0000032d T P256_mulmod
0x00000567 T P256_sqr64
0x000005c5 T P256_sqr128
0x00000641 T P256_sqrmod
0x00000735 T P256_addmod
0x00000791 T P256_submod
0x000007c3 T P256_load_1
0x000007d7 T P256_to_montgomery
0x00000801 T P256_from_montgomery
0x0000081b T P256_point_is_on_curve
0x0000086d T P256_greater_or_equal_than
0x00000887 T P256_negate_mod_m_if
0x000008a9 T P256_copy32
0x000008b5 T P256_copy32_unaligned
0x000008c7 T P256_select
0x00000927 T P256_double_j
0x0000092b T P256_add_j
0x00000983 T P256_div2mod
0x000009e9 T P256_interpreter
0x00000a4d T P256_sqrmod_many_and_mulmod
0x00000a6d T P256_modinv
0x00000af7 T P256_jacobian_to_affine
0x00000b33 T P256_abs_int
0x00000b41 T P256_pointmult
0x00000d03 T P256_ecdh_keygen
0x00000d0b T P256_ecdh_shared_secret
0x00000e09 T __aeabi_uidiv
0x00000e09 T __aeabi_uidivmod
0x00000e35 T __aeabi_idiv
0x00000e35 T __aeabi_idivmod
0x00000e81 T __aeabi_memcpy
0x00000e81 T __aeabi_memcpy4
0x00000e81 T __aeabi_memcpy8
0x00000ea5 T __aeabi_memset
0x00000ea5 T __aeabi_memset4
0x00000ea5 T __aeabi_memset8
0x00000eb3 T __aeabi_memclr
0x00000eb3 T __aeabi_memclr4
0x00000eb3 T __aeabi_memclr8
0x00000eb7 T memset
0x00000ec9 T strlen
0x00000ed7 T strcmp
0x00000ef3 T memcmp
0x00000f0d T strncmp
0x00000f2d T strtok
0x00000f75 T __aeabi_uread4
0x00000f75 T __rt_uread4
0x00000f75 T _uread4
0x00000f89 T strtoul
0x00001001 T __rt_ctype_table
0x00001009 T _strtoul
0x0000112d T GPIO_IRQHandler
0x00001175 T HCI_CommandCompleteEvent
0x000011fd T HCI_CommandStatusEvent
0x00001251 T HCI_DataBufferOverflowEvent
0x0000128d T HCI_DisconnectCmd
0x000012a1 T HCI_EXT_AdvEventNoticeCmd
0x000012a9 T HCI_EXT_BuildRevisionCmd
0x000012e5 T HCI_EXT_ClkDivOnHaltCmd
0x0000130d T HCI_EXT_ConnEventNoticeCmd
0x00001315 T HCI_EXT_DeclareNvUsageCmd
0x00001339 T HCI_EXT_DecryptCmd
0x0000137d T HCI_EXT_DelaySleepCmd
0x000013a5 T HCI_EXT_DisconnectImmedCmd
0x000013cd T HCI_EXT_EnablePTMCmd
0x000013e1 T HCI_EXT_EndModemTestCmd
0x00001409 T HCI_EXT_HaltDuringRfCmd
0x00001431 T HCI_EXT_ModemHopTestTxCmd
0x00001459 T HCI_EXT_ModemTestRxCmd
0x00001481 T HCI_EXT_ModemTestTxCmd
0x000014a5 T HCI_EXT_NumComplPktsLimitCmd
0x000014c9 T HCI_EXT_OnePktPerEvtCmd
0x000014f5 T HCI_EXT_OverlappedProcessingCmd
0x0000151d T HCI_EXT_PERbyChanCmd
0x00001541 T HCI_EXT_PacketErrorRateCmd
0x00001575 T HCI_EXT_ResetSystemCmd
0x0000159d T HCI_EXT_SaveFreqTuneCmd
0x000015c5 T HCI_EXT_SetBDADDRCmd
0x00001601 T HCI_EXT_SetFastTxResponseTimeCmd
0x00001629 T HCI_EXT_SetFreqTuneCmd
0x00001651 T HCI_EXT_SetLocalSupportedFeaturesCmd
0x00001679 T HCI_EXT_SetMaxDtmTxPowerCmd
0x000016a1 T HCI_EXT_SetRxGainCmd
0x000016d1 T HCI_EXT_SetSCACmd
0x000016f9 T HCI_EXT_SetSlaveLatencyOverrideCmd
0x00001721 T HCI_EXT_SetTxPowerCmd
0x00001751 T HCI_ExtTaskRegister
0x0000175d T HCI_GAPTaskRegister
0x00001769 T HCI_HardwareErrorEvent
0x000017b1 T HCI_HostBufferSizeCmd
0x000017e1 T HCI_HostNumCompletedPktCmd
0x0000183d T HCI_Init
0x00001879 T HCI_L2CAPTaskRegister
0x000018a1 T HCI_LE_AddDevToResolvingListCmd
0x000018bd T HCI_LE_AddWhiteListCmd
;0x000018e1 T HCI_LE_ClearAdvSetsCmd
0x00001919 T HCI_LE_ClearResolvingListCmd
0x00001935 T HCI_LE_ClearWhiteListCmd
0x0000196d T HCI_LE_Connection_CTE_Request_EnableCmd
0x00001999 T HCI_LE_Connection_CTE_Response_EnableCmd
0x000019c1 T HCI_LE_ConnectionlessCTE_TransmitEnableCmd
0x000019dd T HCI_LE_ConnectionlessCTE_TransmitParamCmd
0x00001a01 T HCI_LE_ConnectionlessIQ_SampleEnableCmd
0x00001a31 T HCI_LE_CreateConnCancelCmd
0x00001a4d T HCI_LE_CreateConnCmd
0x00001a89 T HCI_LE_EncryptCmd
;0x00001ac1 T HCI_LE_ExtendedCreateConnectionCmd
;0x00001b09 T HCI_LE_LtkReqNegReplyCmd
;0x00001b31 T HCI_LE_LtkReqReplyCmd
0x00001b59 T HCI_LE_PeriodicAdvertisingCreateSyncCancelCmd
0x00001b75 T HCI_LE_PeriodicAdvertisingCreateSyncCmd
0x00001b9d T HCI_LE_PeriodicAdvertisingTerminateSyncCmd
0x00001bb9 T HCI_LE_READ_Anatenna_InfoCmd
0x00001bd9 T HCI_LE_RandCmd
0x00001c09 T HCI_LE_ReadAdvChanTxPowerCmd
0x00001c29 T HCI_LE_ReadBufSizeCmd
0x00001c4d T HCI_LE_ReadChannelMapCmd
0x00001c99 T HCI_LE_ReadLocalSupportedFeaturesCmd
0x00001cb9 T HCI_LE_ReadMaxDataLengthCmd
;0x00001ce9 T HCI_LE_ReadMaximumAdvDataLengthCmd
;0x00001d11 T HCI_LE_ReadNumberOfSupportAdvSetCmd
0x00001d71 T HCI_LE_ReadPhyMode
0x00001db1 T HCI_LE_ReadRemoteUsedFeaturesCmd
0x00001dc5 T HCI_LE_ReadResolvingListSizeCmd
0x00001de5 T HCI_LE_ReadSuggestedDefaultDataLengthCmd
0x00001e15 T HCI_LE_ReadSupportedStatesCmd
0x00001e3d T HCI_LE_ReadWhiteListSizeCmd
0x00001e9d T HCI_LE_ReceiverTestCmd
;0x00001eb1 T HCI_LE_RemoveAdvSetCmd
0x00001ee9 T HCI_LE_RemoveResolvingListCmd
0x00001f0d T HCI_LE_RemoveWhiteListCmd
0x00001f31 T HCI_LE_SetAddressResolutionEnableCmd
0x00001f4d T HCI_LE_SetAdvDataCmd
0x00001f69 T HCI_LE_SetAdvEnableCmd
0x00001f85 T HCI_LE_SetAdvParamCmd
0x00001fb1 T HCI_LE_SetDataLengthCmd
0x00001fd9 T HCI_LE_SetDefaultPhyMode
0x00001ff5 T HCI_LE_SetEventMaskCmd
;0x0000202d T HCI_LE_SetExtAdvDataCmd
;0x0000204d T HCI_LE_SetExtAdvEnableCmd
;0x0000206d T HCI_LE_SetExtAdvParamCmd
;0x000020c1 T HCI_LE_SetExtAdvSetRandomAddressCmd
;0x000020dd T HCI_LE_SetExtScanRspDataCmd
;0x000020fd T HCI_LE_SetExtendedScanEnableCmd
;0x00002119 T HCI_LE_SetExtendedScanParametersCmd
;0x0000213d T HCI_LE_SetHostChanClassificationCmd
0x00002159 T HCI_LE_SetPeriodicAdvDataCmd
0x00002175 T HCI_LE_SetPeriodicAdvEnableCmd
0x00002191 T HCI_LE_SetPeriodicAdvParameterCmd
0x000021ad T HCI_LE_SetPhyMode
;0x000021c5 T HCI_LE_SetRandomAddressCmd
0x000021e9 T HCI_LE_SetResolvablePrivateAddressTimeoutCmd
0x00002219 T HCI_LE_SetScanEnableCmd
0x00002235 T HCI_LE_SetScanParamCmd
0x00002255 T HCI_LE_SetScanRspDataCmd
0x00002271 T HCI_LE_Set_ConnectionCTE_ReceiveParamCmd
0x0000229d T HCI_LE_Set_ConnectionCTE_TransmitParamCmd
0x000022e1 T HCI_LE_StartEncyptCmd
0x000022f5 T HCI_LE_TestEndCmd
0x0000231d T HCI_LE_TransmitterTestCmd
0x00002339 T HCI_LE_WriteSuggestedDefaultDataLengthCmd
0x00002371 T HCI_NumOfCompletedPacketsEvent
0x00002401 T HCI_PPLUS_AdvEventDoneNoticeCmd
0x00002421 T HCI_PPLUS_ConnEventDoneNoticeCmd
0x00002461 T HCI_PPLUS_DateLengthChangedNoticeCmd
0x000024a1 T HCI_PPLUS_ExtendTRXCmd
0x000024bd T HCI_PPLUS_PhyUpdateNoticeCmd
0x000024fd T HCI_ProcessEvent
0x00002551 T HCI_ReadBDADDRCmd
0x00002571 T HCI_ReadLocalSupportedCommandsCmd
0x00002589 T HCI_ReadLocalSupportedFeaturesCmd
0x000025ad T HCI_ReadLocalVersionInfoCmd
0x000025f5 T HCI_ReadRemoteVersionInfoCmd
0x00002625 T HCI_ReadRssiCmd
0x00002651 T HCI_ReadTransmitPowerLevelCmd
0x0000267d T HCI_ResetCmd
0x000026a9 T HCI_ReverseBytes
0x000026c9 T HCI_SMPTaskRegister
0x000026d5 T HCI_SendCommandCompleteEvent
0x0000277d T HCI_SendCommandStatusEvent
0x0000279d T HCI_SendControllerToHostEvent
0x000027e9 T HCI_SendDataPkt
0x00002819 T HCI_SetControllerToHostFlowCtrlCmd
0x0000285d T HCI_SetEventMaskCmd
0x0000288d T HCI_TestAppTaskRegister
0x00002899 T HCI_ValidConnTimeParams
0x000028d9 T HCI_VendorSpecifcCommandCompleteEvent
0x000028e9 T HCI_bm_alloc
0x000028f1 T HardFault_Handler
0x00002909 T HardFault_IRQHandler
0x000029dd T LL_AddResolvingListLDevice
0x00002a95 T LL_AddWhiteListDevice
0x00002b11 T LL_AdvReportCback
0x00002ca1 T LL_CTE_Report_FailedCback
0x00002d0d T LL_ChanMapUpdate
;0x00002e2d T LL_ClearAdvSets
0x00002f41 T LL_ClearResolvingList
0x00002fbd T LL_ClearWhiteList
0x00003011 T LL_ConnActive
0x00003039 T LL_ConnParamUpdateCback
0x000030e5 T LL_ConnUpdate
0x000031b9 T LL_ConnectionCompleteCback
0x000032d5 T LL_ConnectionIQReportCback
0x000033e5 T LL_Connection_CTE_Request_Enable
0x00003499 T LL_Connection_CTE_Response_Enable
0x00003505 T LL_ConnectionlessCTE_TransmitEnable
0x000035e5 T LL_ConnectionlessCTE_TransmitParam
0x000036c1 T LL_ConnectionlessIQReportCback
0x000037c5 T LL_ConnectionlessIQ_SampleEnable
0x00003901 T LL_CreateConn
0x00003949 T LL_CreateConn0
0x00003c91 T LL_CreateConnCancel
0x00003ca9 T LL_CreateConnCancel0
0x00003d59 T LL_CtrlToHostFlowControl
0x00003d71 T LL_DataLengthChangeCback
0x00003e3d T LL_DirectTestEnd
0x00003e95 T LL_DirectTestTxTest
0x00003eb1 T LL_Disconnect
0x00003ec9 T LL_Disconnect0
0x00003f41 T LL_DisconnectCback
0x00003fc5 T LL_ENC_AES128_Encrypt
0x00003fdd T LL_ENC_AES128_Encrypt0
0x000040ed T LL_ENC_Decrypt
0x00004105 T LL_ENC_Decrypt0
0x00004261 T LL_ENC_Encrypt
0x00004278 T LL_ENC_Encrypt0
0x000043c1 T LL_ENC_GenDeviceIV
0x000043f1 T LL_ENC_GenDeviceSKD
0x00004421 T LL_ENC_GenerateNonce
0x00004459 T LL_ENC_GeneratePseudoRandNum
0x00004469 T LL_ENC_GenerateTrueRandNum
0x00004489 T LL_ENC_LoadKey
0x000044e1 T LL_ENC_ReverseBytes
0x00004501 T LL_ENC_sm_ah
0x00004543 T LL_EXT_AdvEventNotice
0x00004547 T LL_EXT_BuildRevision
0x0000454b T LL_EXT_ClkDivOnHalt
0x0000454f T LL_EXT_ConnEventNotice
0x00004553 T LL_EXT_DeclareNvUsage
0x00004557 T LL_EXT_Decrypt
0x0000455b T LL_EXT_DelaySleep
0x0000455f T LL_EXT_DisconnectImmed
0x00004563 T LL_EXT_EndModemTest
0x00004567 T LL_EXT_HaltDuringRf
0x0000456d T LL_EXT_Init_IQ_pBuff
0x00004581 T LL_EXT_MapPmIoPort
0x00004585 T LL_EXT_ModemHopTestTx
0x00004589 T LL_EXT_ModemTestRx
0x0000458d T LL_EXT_ModemTestTx
0x00004591 T LL_EXT_NumComplPktsLimit
0x000045ad T LL_EXT_OnePacketPerEvent
0x000045b1 T LL_EXT_OverlappedProcessing
0x000045b5 T LL_EXT_PERbyChan
0x000045b9 T LL_EXT_PacketErrorRate
0x000045bd T LL_EXT_PacketErrorRateCback
0x000045f9 T LL_EXT_ResetSystem
0x000045fd T LL_EXT_SaveFreqTune
0x00004601 T LL_EXT_SetBDADDR
0x00004605 T LL_EXT_SetFastTxResponseTime
0x00004609 T LL_EXT_SetFreqTune
0x0000460d T LL_EXT_SetLocalSupportedFeatures
0x00004611 T LL_EXT_SetMaxDtmTxPower
0x00004615 T LL_EXT_SetRxGain
0x00004619 T LL_EXT_SetRxGainCback
0x00004635 T LL_EXT_SetSCA
0x00004639 T LL_EXT_SetSlaveLatencyOverride
0x0000463d T LL_EXT_SetTxPower
0x00004679 T LL_EXT_SetTxPowerCback
0x00004699 T LL_EncChangeCback
0x00004715 T LL_EncKeyRefreshCback
;0x0000478d T LL_EncLtkNegReply
;0x000047d9 T LL_EncLtkReply
0x00004831 T LL_EncLtkReqCback
0x000048e5 T LL_Encrypt
0x00004a8d T LL_ExtAdvReportCback
;0x00004bfd T LL_ExtendedCreateConnection
0x00004e25 T LL_IRQHandler
0x00004eb1 T LL_Init
;0x00005045 T LL_InitConnectContext
0x0000511d T LL_InitExtendedAdv
0x000051b5 T LL_InitExtendedScan
0x000051c5 T LL_InitPeriodicAdv
0x00005291 T LL_NumEmptyWlEntries
0x000052a5 T LL_PLUS_DisableSlaveLatency
0x00005435 T LL_PLUS_EnableSlaveLatency
0x0000548d T LL_PLUS_GetAdvDataExtendData
0x00005495 T LL_PLUS_GetScanRequestExtendData
0x000054bd T LL_PLUS_GetScanerAddr
0x000054d5 T LL_PLUS_PerStasReadByChn
0x00005519 T LL_PLUS_PerStatsReset
0x00005535 T LL_PLUS_PerStats_Init
0x00005545 T LL_PLUS_SetAdvDataFilterCB
0x00005551 T LL_PLUS_SetScanRequestData
0x00005579 T LL_PLUS_SetScanRequestFilterCB
0x00005585 T LL_PLUS_SetScanRsqData
0x000055b1 T LL_PLUS_SetScanRsqDataByIndex
0x000055bd T LL_PeriodicAdvertisingCreateSync
0x0000560d T LL_PeriodicAdvertisingCreateSyncCancel
0x00005635 T LL_PeriodicAdvertisingTerminateSync
0x00005655 T LL_PhyUpdate
0x0000566d T LL_PhyUpdate0
0x00005725 T LL_PhyUpdateCompleteCback
0x000057c9 T LL_PrdAdvReportCback
0x000058b5 T LL_PrdAdvSyncEstablishedCback
0x00005989 T LL_PrdAdvSyncLostCback
0x000059f1 T LL_ProcessEvent
0x00005a09 T LL_ProcessEvent0  
0x00005dd9 T LL_PseudoRand
0x00005ddd T LL_READ_Anatenna_Info
0x00005df1 T LL_RX_bm_alloc
0x00005e05 T LL_Rand
0x00005e65 T LL_RandCback
0x00005e89 T LL_ReadAdvChanTxPower
0x00005eb9 T LL_ReadBDADDR
0x00005edd T LL_ReadCarrSens
0x00005ef9 T LL_ReadChanMap
0x00005f31 T LL_ReadFoff
0x00005f71 T LL_ReadLocalSupportedFeatures
0x00005f8d T LL_ReadLocalVersionInfo
;0x00005fa5 T LL_ReadMaximumAdvDataLength
;0x00005fc5 T LL_ReadNumberOfSupportAdvSet
0x00006021 T LL_ReadRemoteUsedFeatures
0x00006061 T LL_ReadRemoteUsedFeaturesCompleteCback
0x000060d5 T LL_ReadRemoteVersionInfo
0x00006131 T LL_ReadRemoteVersionInfoCback
0x000061a9 T LL_ReadResolvingListSize
0x000061b1 T LL_ReadRssi
0x000061e5 T LL_ReadSupportedStates
0x00006269 T LL_ReadTxPowerLevel
0x000062e5 T LL_ReadWlSize
;0x00006335 T LL_RemoveAdvSet
0x000064a5 T LL_RemoveResolvingListDevice
0x00006561 T LL_RemoveWhiteListDevice
0x000065f1 T LL_Reset
0x00006609 T LL_Reset0
0x00006791 T LL_RxDataCompleteCback
0x00006831 T LL_SetAddressResolutionEnable
0x00006881 T LL_SetAdvControl
0x00006899 T LL_SetAdvControl0
0x00006a05 T LL_SetAdvData
0x00006a6d T LL_SetAdvParam
0x00006a9d T LL_SetAdvParam0
0x00006df9 T LL_SetDataLengh
0x00006e11 T LL_SetDataLengh0
0x00006ead T LL_SetDefaultPhyMode
;0x00006edd T LL_SetExtAdvData
;0x00006fc5 T LL_SetExtAdvEnable
;0x00007205 T LL_SetExtAdvParam
;0x000073a9 T LL_SetExtAdvSetRandomAddress
;0x000073f5 T LL_SetExtScanRspData
;0x00007471 T LL_SetExtendedScanEnable
;0x000074b5 T LL_SetExtendedScanParameters
0x0000751d T LL_SetPeriodicAdvData
0x000075cd T LL_SetPeriodicAdvEnable
0x00007715 T LL_SetPeriodicAdvParameter
0x000077e1 T LL_SetPhyMode
0x000077fd T LL_SetPhyMode0
;0x000078c9 T LL_SetRandomAddress
0x00007929 T LL_SetResolvablePrivateAddressTimeout
0x00007935 T LL_SetScanControl
0x0000794d T LL_SetScanControl0
0x00007a59 T LL_SetScanParam
0x00007a75 T LL_SetScanParam0
0x00007b15 T LL_SetScanRspData
0x00007b65 T LL_SetTxPowerLevel
0x00007b91 T LL_Set_ConnectionCTE_ReceiveParam
0x00007c99 T LL_Set_ConnectionCTE_TransmitParam
0x00007e01 T LL_StartEncrypt
0x00007f01 T LL_TX_bm_alloc
0x00007f1d T LL_TxData
0x00007f35 T LL_TxData0
0x00007fb9 T LL_WriteSuggestedDefaultDataLength
0x00007ff9 T LL_evt_schedule
;0x000080b5 T LL_extAdvTimerExpProcess
;0x000080b9 T LL_extInitTimerExpProcess
;0x000080bb T LL_extScanTimerExpProcess
0x000080bd T LL_master_conn_event
0x000080d5 T LL_master_conn_event0
0x0000826d T LL_prdAdvTimerExpProcess
0x00008271 T LL_prdScanTimerExpProcess
0x00008275 T LL_set_default_conn_params
0x0000828d T LL_set_default_conn_params0
0x000082b9 T LL_slave_conn_event
0x000082d1 T LL_slave_conn_event0
0x00008481 T NMI_Handler
0x000084cd T PendSV_Handler
0x00008545 T TIM1_IRQHandler
0x00008901 T WaitRTCCount
0x00008961 T __ARM_common_switch8
0x0000961d T _spif_read_status_reg
0x00009645 T _spif_wait_nobusy
0x00009769 T app_sleep_process
0x00009779 T app_wakeup_process
0x00009789 T ate_fun_test
0x00009d11 T ate_sleep_process
0x0000a121 T ate_wakeup_process
0x0000a1e9 T bit_to_byte
0x0000a201 T ble_crc24_gen
0x0000a361 T boot_init
0x0000a379 T boot_init0
0x0000a3c1 T boot_m0
0x0000a535 T byte_to_bit
0x0000a549 T calculate_whiten_seed
0x0000a5c1 T clear_timer
0x0000a5cb T clear_timer_int
0x0000a5d1 T clk_get_pclk
;0x0000a5ed T clk_init
0x0000a681 T clk_set_pclk_div
0x0000a6a1 T clk_spif_ref_clk
0x0000a6f9 T config_RTC
0x0000a711 T config_RTC0
0x0000a755 T rom_crc16
0x0000a791 T debug_print
0x0000a921 T disableSleep
0x0000a975 T drv_disable_irq
0x0000a99d T drv_enable_irq
0x0000a9c9 T drv_irq_init
0x0000a9fd T dwc_connect
0x0000aa35 T dwc_data_process
0x0000abd1 T dwc_loop
0x0000ace1 T efuse_read
0x0000aead T enableSleep
0x0000aeb9 T enterSleepProcess
0x0000aed1 T enterSleepProcess0
0x0000afa1 T enter_sleep_off_mode
0x0000afb9 T enter_sleep_off_mode0
0x0000afe9 T getMcuPrecisionCount
0x0000aff5 T getPN23RandNumber
0x0000b01d T getRxBufferFree
0x0000b031 T getRxBufferSize
0x0000b051 T getSleepMode
0x0000b05d T getTxBufferFree
0x0000b071 T getTxBufferSize
0x0000b0b1 T get_rx_read_ptr
0x0000b0b9 T get_rx_write_ptr
0x0000b0c1 T get_sleep_flag
0x0000b0cd T get_timer_count
0x0000b0d1 T get_timer_int
0x0000b0d9 T get_tx_read_ptr
0x0000b0e1 T get_tx_write_ptr
0x0000b0e9 T gpio_cfg_analog_io
0x0000b119 T gpio_dir
0x0000b15d T gpio_fmux_control
0x0000b179 T gpio_fmux_set
0x0000b1b1 T gpio_in_trigger
0x0000b219 T gpio_init
0x0000b22d T gpio_interrupt_set
0x0000b249 T gpio_pull_set
0x0000b291 T gpio_read
0x0000b2b5 T gpio_wakeup_set
0x0000b319 T gpio_write
0x0000b379 T rom_uart_init
0x0000b52d T hciInitEventMasks
0x0000b555 T isSleepAllow
0x0000b561 T isTimer1Running
0x0000b571 T isTimer4Running
0x0000b581 T jump_area_init
0x0000b5a9 T ll24BitTimeCompare
0x0000b609 T llAdjSlaveLatencyValue
0x0000b629 T llAllocConnId
0x0000b679 T llAllocateSyncHandle
0x0000b6a9 T llAtLeastTwoChans
0x0000b6ed T llCalcMaxScanTime
0x0000b745 T llCalcScaFactor
0x0000b76d T llCalcTimerDrift
0x0000b7b1 T llCheckForLstoDuringSL
0x0000b7ed T llCheckWhiteListUsage
0x0000b809 T llConnCleanup
0x0000b839 T llConnTerminate
0x0000b851 T llConnTerminate0
0x0000b87d T llConvertCtrlProcTimeoutToEvent
0x0000b899 T llConvertLstoToEvent
0x0000b8bd T llDeleteSyncHandle
0x0000b8ed T llDequeueCtrlPkt
0x0000b929 T llDequeueDataQ
0x0000b953 T llEnqueueCtrlPkt
0x0000b98b T llEnqueueDataQ
0x0000b9b1 T llEqAlreadyValidAddr
0x0000b9b5 T llEqSynchWord
0x0000b9c9 T llEqualBytes
0x0000b9e9 T llEventDelta
0x0000b9fd T llEventInRange
0x0000ba1b T llGenerateCRC
0x0000ba3d T llGenerateValidAccessAddr
0x0000ba6d T llGetNextAdvChn
0x0000bac1 T llGetNextAuxAdvChn
0x0000bae5 T llGetNextDataChan
0x0000bb23 T llGetNextDataChanCSA2
0x0000bb81 T llGtSixConsecZerosOrOnes
0x0000bbb3 T llGtTwentyFourTransitions
0x0000bbe5 T llInitFeatureSet
0x0000bc1d T llInitFeatureSet2MPHY
0x0000bc45 T llInitFeatureSetCodedPHY
0x0000bc6d T llInitFeatureSetDLE
0x0000bc89 T llLtTwoChangesInLastSixBits
0x0000bcb9 T llMasterEvt_TaskEndOk
0x0000be05 T llMemCopyDst
0x0000be1b T llMemCopySrc
0x0000be35 T llOneBitSynchWordDiffer
0x0000be4d T llPduLengthManagmentReset
0x0000bf01 T llPduLengthUpdate
0x0000c00d T llPendingUpdateParam
0x0000c051 T llPhyModeCtrlReset
0x0000c0b9 T llPhyModeCtrlUpdateNotify
0x0000c0f5 T llPrdAdvDecideNextChn
0x0000c185 T llProcessChanMap
0x0000c1d1 T llProcessMasterControlPacket
0x0000c1e9 T llProcessMasterControlPacket0
0x0000c5b9 T llProcessMasterControlProcedures
0x0000ca85 T llProcessRxData
0x0000ca9d T llProcessRxData0
0x0000cc31 T llProcessSlaveControlPacket
0x0000cc49 T llProcessSlaveControlPacket0
0x0000d169 T llProcessSlaveControlProcedures
0x0000d4d1 T llProcessTxData
0x0000d4e9 T llProcessTxData0
0x0000d55d T llReleaseAllConnId
0x0000d561 T llReleaseConnId
0x0000d579 T llReleaseConnId0
0x0000d5f5 T llReplaceCtrlPkt
0x0000d60d T llResetConnId
0x0000d6ed T llResetRfCounters
0x0000d701 T llSecAdvAllow
0x0000d769 T llSetNextDataChan
0x0000d839 T llSetNextPhyMode
0x0000d8a9 T llSetupAdv
0x0000d8c1 T llSetupAdv0
0x0000d949 T llSetupAdvExtIndPDU
0x0000db4d T llSetupAuxAdvIndPDU
0x0000dda1 T llSetupAuxChainIndPDU
0x0000df91 T llSetupAuxConnectReqPDU
0x0000e025 T llSetupAuxConnectRspPDU
0x0000e09d T llSetupAuxScanRspPDU
0x0000e109 T llSetupAuxSyncIndPDU
0x0000e2c1 T llSetupCTEReq
0x0000e38d T llSetupCTERsp
0x0000e457 T llSetupConn
0x0000e459 T llSetupDataLenghtReq
0x0000e4d5 T llSetupDataLenghtRsp
0x0000e551 T llSetupDirectedAdvEvt
0x0000e6a1 T llSetupEncReq
0x0000e725 T llSetupEncRsp
0x0000e7b1 T llSetupExtAdvEvent
;0x0000eb09 T llSetupExtInit
;0x0000eb81 T llSetupExtScan
0x0000ec01 T llSetupFeatureSetReq
0x0000ec63 T llSetupFeatureSetRsp
0x0000ecc1 T llSetupInit
0x0000ed39 T llSetupNextMasterEvent
0x0000ed51 T llSetupNextMasterEvent0
0x0000ede1 T llSetupNextSlaveEvent
0x0000edf9 T llSetupNextSlaveEvent0
0x0000ef65 T llSetupNonConnectableAdvEvt
0x0000f075 T llSetupPauseEncReq
0x0000f0c5 T llSetupPauseEncRsp
0x0000f121 T llSetupPhyReq
0x0000f177 T llSetupPhyRsp
0x0000f1cd T llSetupPhyUpdateInd
0x0000f239 T llSetupPrdAdvEvent
0x0000f3b9 T llSetupPrdScan
0x0000f44d T llSetupRejectExtInd
0x0000f479 T llSetupRejectInd
0x0000f4a5 T llSetupScan
0x0000f4bd T llSetupScan0
0x0000f54d T llSetupScanInit
0x0000f55d T llSetupScannableAdvEvt
0x0000f66d T llSetupSecAdvEvt
0x0000f6e9 T llSetupSecConnectableAdvEvt
0x0000f7c1 T llSetupSecInit
0x0000f875 T llSetupSecNonConnectableAdvEvt
0x0000f94d T llSetupSecScan
0x0000fa19 T llSetupSecScannableAdvEvt
0x0000faf1 T llSetupStartEncReq
;0x0000fb15 T llSetupStartEncRsp
0x0000fb59 T llSetupSyncInfo
0x0000fc2d T llSetupTermInd
0x0000fc91 T llSetupUndirectedAdvEvt
0x0000fda5 T llSetupUnknownRsp
0x0000fdf9 T llSetupUpdateChanReq
0x0000fe6d T llSetupUpdateParamReq
0x0000ff05 T llSetupVersionIndReq
0x0000ff79 T llSlaveEvt_TaskAbort
0x0000ff95 T llSlaveEvt_TaskEndOk
0x0000ffad T llSlaveEvt_TaskEndOk0
0x00010181 T llTrxNumAdaptiveConfig
0x0001019b T llValidAccessAddr
0x000101e1 T llWaitUs
0x00010209 T llWriteTxData
0x0001028f T ll_CalcRandomAddr
0x000102cd T ll_ResolveRandomAddrs
0x00010315 T ll_addTask
0x00010445 T ll_add_adv_task
0x00010461 T ll_add_adv_task_periodic
0x0001047d T ll_adptive_adj_next_time
0x000104fd T ll_adptive_smart_window
0x000105ad T ll_adv_scheduler
0x000105c9 T ll_adv_scheduler_periodic
0x000105e5 T ll_allocAuxAdvTimeSlot
0x00010679 T ll_allocAuxAdvTimeSlot_prd
0x00010719 T ll_debug_output
0x00010731 T ll_deleteTask
0x00010765 T ll_delete_adv_task
0x00010781 T ll_delete_adv_task_periodic
0x0001079d T ll_ext_adv_schedule_next_event
0x000107c1 T ll_ext_init_schedule_next_event
0x000107dd T ll_ext_scan_schedule_next_event
0x000107f9 T ll_generateExtAdvDid
0x00010801 T ll_generateTxBuffer
0x000109c1 T ll_getFirstAdvChn
0x000109cd T ll_getRPAListEntry
0x00010a39 T ll_get_next_active_conn
0x00010aa1 T ll_get_next_timer
0x00010add T ll_hw_clr_irq
0x00010aed T ll_hw_config
0x00010b6d T ll_hw_get_anchor
0x00010b79 T ll_hw_get_fsm_status
0x00010b89 T ll_hw_get_iq_RawSample
0x00010bbd T ll_hw_get_irq_status
0x00010bcd T ll_hw_get_last_ack
0x00010be9 T ll_hw_get_loop_cycle
0x00010bf5 T ll_hw_get_loop_time
0x00010c01 T ll_hw_get_nAck
0x00010c11 T ll_hw_get_rfifo_depth
0x00010c25 T ll_hw_get_rfifo_info
0x00010c45 T ll_hw_get_rxPkt_CrcErr_num
0x00010c55 T ll_hw_get_rxPkt_CrcOk_num
0x00010c69 T ll_hw_get_rxPkt_Total_num
0x00010c79 T ll_hw_get_rxPkt_num
0x00010c85 T ll_hw_get_rxPkt_stats
0x00010c9d T ll_hw_get_snNesn
0x00010cad T ll_hw_get_tfifo_info
0x00010ccd T ll_hw_get_tfifo_wrptr
0x00010cdd T ll_hw_get_tr_mode
0x00010ced T ll_hw_get_txAck
0x00010cf9 T ll_hw_go
0x00010df9 T ll_hw_ign_rfifo
0x00010e05 T ll_hw_process_RTO
0x00010e6d T ll_hw_read_rfifo
0x00010ee9 T ll_hw_read_rfifo_pplus
0x00010f51 T ll_hw_read_rfifo_zb
0x00010fad T ll_hw_read_tfifo_packet
0x00010ff5 T ll_hw_read_tfifo_rtlp
0x0001100d T ll_hw_read_tfifo_rtlp0
0x000110b1 T ll_hw_rst_rfifo
0x000110e9 T ll_hw_rst_tfifo
0x000110f5 T ll_hw_set_ant_pattern
0x00011101 T ll_hw_set_ant_switch_mode
0x00011115 T ll_hw_set_ant_switch_timing
0x0001112d T ll_hw_set_crc_fmt
0x0001113d T ll_hw_set_cte_rxSupp
0x00011155 T ll_hw_set_cte_txSupp
0x00011169 T ll_hw_set_empty_head
0x00011175 T ll_hw_set_irq
0x00011181 T ll_hw_set_loop_nack_num
0x0001118d T ll_hw_set_loop_timeout
0x000111a1 T ll_hw_set_pplus_pktfmt
0x000111cd T ll_hw_set_rtlp
0x0001121d T ll_hw_set_rtlp_1st
0x00011265 T ll_hw_set_rtx
0x00011279 T ll_hw_set_rx_timeout
0x00011285 T ll_hw_set_rx_timeout_1st
0x00011291 T ll_hw_set_rx_tx_interval
0x000112a5 T ll_hw_set_srx
0x000112b9 T ll_hw_set_stx
0x000112cd T ll_hw_set_tfifo_space
0x000112e5 T ll_hw_set_timing
0x00011381 T ll_hw_set_trlp
0x000113c9 T ll_hw_set_trx
0x000113dd T ll_hw_set_trx_settle
0x000113f1 T ll_hw_set_tx_rx_interval
0x00011405 T ll_hw_set_tx_rx_release
0x00011421 T ll_hw_trigger
0x00011445 T ll_hw_trx_settle_config
0x00011489 T ll_hw_tx2rx_timing_config
0x000114dd T ll_hw_update
0x00011539 T ll_hw_update_rtlp_mode
0x00011579 T ll_hw_update_trlp_mode
0x000115c1 T ll_hw_write_tfifo
0x00011649 T ll_isAddrInWhiteList
0x000116a9 T ll_isFirstAdvChn
0x000116c7 T ll_isIrkAllZero
0x000116dd T ll_isLegacyAdv
0x000116ed T ll_parseExtHeader
0x000117a9 T ll_prd_adv_schedule_next_event
0x000117cd T ll_prd_scan_schedule_next_event
0x000117e9 T ll_processBasicIRQ
0x00013401 T ll_processExtAdvIRQ
0x00013405 T ll_processExtInitIRQ
0x00013409 T ll_processExtScanIRQ
0x0001340d T ll_processMissMasterEvt
0x000134ed T ll_processMissSlaveEvt
0x000135f5 T ll_processPrdAdvIRQ
0x000135f9 T ll_processPrdScanIRQ
0x000135fd T ll_readLocalIRK
0x00013661 T ll_readPeerIRK
0x000136c5 T ll_read_rxfifo
0x000136cd T ll_read_rxfifo0
0x00013761 T ll_schedule_next_event
0x00013771 T ll_scheduler
0x00013789 T ll_scheduler0
0x00013a11 T ll_updateAuxAdvTimeSlot
0x00013a39 T ll_updateExtAdvRemainderTime
0x00013ab9 T log_clr_putc
0x00013ac5 T log_debug_level
0x00013ad9 T log_get_debug_level
0x00013ae5 T log_printf
0x00013b05 T log_set_putc
0x00013b11 T log_vsprintf
0x00013f09 T move_to_master_function
0x00013f21 T move_to_master_function0
0x0001406d T move_to_slave_function
0x00014085 T move_to_slave_function0
0x00014439 T osalAddTimer
0x000144a9 T osalDeleteTimer
0x000144b5 T osalFindTimer
0x000144d5 T osalTimeUpdate
0x00014541 T osalTimeUpdate1
0x0001457d T osalTimerInit
0x00014589 T osalTimerUpdate
0x00014621 T osal_CbTimerInit
0x00014641 T osal_CbTimerProcessEvent
0x000146a9 T osal_CbTimerStart
0x00014711 T osal_CbTimerStop
0x00014751 T osal_CbTimerUpdate
0x000147a1 T osal_ConvertUTCSecs
0x00014841 T osal_ConvertUTCTime
0x00014949 T osal_GetSystemClock
0x00014955 T osal_bm_adjust_header
0x0001497d T osal_bm_adjust_tail
0x000149a9 T osal_bm_alloc
0x000149d9 T osal_bm_free
0x00014a21 T osal_buffer_uint24
0x00014a2f T osal_buffer_uint32
0x00014a41 T osal_build_uint16
0x00014a4d T osal_build_uint32
0x00014a89 T osal_clear_event
0x00014abd T osal_getClock
0x00014ac9 T osal_get_timeoutEx
0x00014aed T osal_init_system
0x00014b1d T osal_isbufset
0x00014b3d T osal_mem_alloc
0x00014c01 T osal_mem_free
0x00014c25 T osal_mem_init
0x00014c8d T osal_mem_kick
0x00014cb5 T osal_mem_set_heap
0x00014ccd T osal_memcmp
0x00014ce9 T osal_memcpy
0x00014cf9 T osal_memdup
0x00014d15 T osal_memset
0x00014d1d T osal_msg_allocate
0x00014d43 T osal_msg_deallocate
0x00014d65 T osal_msg_dequeue
0x00014d91 T osal_msg_enqueue
0x00014dc3 T osal_msg_enqueue_max
0x00014e6d T osal_msg_extract
0x00014e9d T osal_msg_find
0x00014ed1 T osal_msg_push
0x00014eeb T osal_msg_push_front
0x00014ef5 T osal_msg_receive
0x00014f59 T osal_msg_send
0x00014f7d T osal_next_timeout
0x00014fa5 T osal_pwrmgr_device
0x00014fb1 T osal_pwrmgr_init
0x00014fc1 T osal_pwrmgr_powerconserve
0x00014fd9 T osal_pwrmgr_powerconserve0
0x000150f9 T osal_pwrmgr_task_state
0x00015129 T osal_rand
0x00015145 T osal_revmemcpy
0x00015159 T osal_run_system
0x000151f5 T osal_self
0x00015201 T osal_setClock
0x0001520d T osal_set_event
0x00015259 T osal_start_reload_timer
0x00015285 T osal_start_system
0x0001528b T osal_start_timerEx
0x000152b3 T osal_stop_timerEx
0x000152dd T osal_strlen
0x000152e5 T osal_timer_num_active
0x00015315 T phy_sec_app_key
0x0001531d T phy_sec_decrypt
0x0001532d T phy_sec_efuse_lock
0x00015339 T phy_sec_encrypt
0x00015349 T phy_sec_init
0x0001540d T phy_sec_key_valid
0x00015b19 T prog_process_data
0x00015c51 T prog_uart_command
0x00015c71 T prog_uart_fct_command
0x00015c8d T prog_uart_handle
0x00015cbd T read_LL_remainder_time
0x00015cc9 T read_current_fine_time
0x00015cf1 T read_ll_adv_remainder_time
0x00015cfd T reset_conn_buf
;0x00015d75 T rf_calibrate
;0x00015ded T rf_init
;0x00015dfd T rf_phy_ana_cfg
;0x00015f0d T rf_phy_bb_cfg
;0x00016085 T rf_phy_change_cfg
;0x00016129 T rf_phy_direct_test_ate
;0x00016669 T rf_phy_get_pktFoot
;0x000166c1 T rf_phy_ini
;0x000166dd T rf_phy_set_txPower
;0x00016701 T rf_rxDcoc_cfg
;0x00016811 T rf_tpCal_cfg
;0x0001683d T rf_tpCal_cfg_avg
;0x000168ed T rf_tpCal_gen_cap_arrary
;0x00016929 T rf_tp_cal
0x00016a09 T rom_board_init
0x00016ab5 T rtc_clear
0x00016ad1 T rtc_config_prescale
0x00016b15 T rtc_get_counter
0x00016b25 T rtc_start
0x00016b35 T rtc_stop
0x00016b45 T setSleepMode
0x00016b51 T set_access_address
0x00016b5d T set_channel
0x00016b9d T set_crc_seed
0x00016bb5 T set_gpio_pull_down_ate
0x00016bcb T set_gpio_pull_up_ate
0x00016be1 T set_int
0x00016bed T set_max_length
0x00016c01 T set_sleep_flag
0x00016c2d T set_timer
0x00016cc9 T set_whiten_seed
0x00016d49 T spif_cmd
0x00016dc5 T spif_config
0x00016ea1 T spif_erase_all
0x00016ed1 T spif_erase_block64
0x00016f55 T spif_erase_chip
0x00016fa9 T spif_erase_sector
0x00017029 T spif_flash_size
0x0001703d T spif_flash_status_reg_0
0x00017047 T spif_flash_status_reg_1
0x00017051 T spif_init
0x0001713d T spif_rddata
0x00017165 T spif_read
0x0001717d T spif_read_dma
0x00017209 T spif_read_id
0x000172cd T spif_release_deep_sleep
0x00017349 T spif_set_deep_sleep
0x0001736d T spif_wrdata
0x00017395 T spif_write
0x0001744d T spif_write_dma
0x000174f9 T spif_write_protect
0x00017591 T sram_ret_patch
0x00017609 T update_rx_read_ptr
0x00017635 T update_rx_write_ptr
0x00017659 T update_tx_read_ptr
0x00017685 T update_tx_write_ptr
0x000176a9 T wakeupProcess
0x000176c5 T wakeupProcess0
0x000178a5 T wakeup_init
0x000178bd T wakeup_init0
0x0001798d T zigbee_crc16_gen
0x00017be0 D g_hclk_table
0x00017c00 D supportedCmdsTable
0x00017c44 D hciCmdTable
0x00017c4c D SCA
0x1fff0818 D hclk_per_us
0x1fff081c D hclk_per_us_shift
0x1fff0828 D s_prog_time_save
0x1fff082c D s_prog_timeout
0x1fff0830 D DFL_ENTRY_BASE
0x1fff0850 D receive_timeout_flag
0x1fff0860 D osal_sys_tick
0x1fff0864 D g_timer4_irq_pending_time
0x1fff0874 D g_hclk
0x1fff0878 D m_in_critical_region 
0x1fff0888 D s_rom_debug_level
0x1fff0894 D s_spif_ctx
0x1fff08b8 D osal_qHead
0x1fff08c0 D baseTaskID
0x1fff08cc D OSAL_timeSeconds
0x1fff08d0 D osalMemStat
0x1fff08d4 D theHeap
0x1fff08d8 D ff1
0x1fff08dc D heapSize
0x1fff08e4 D ll_remain_time
0x1fff08e8 D pwrmgr_attribute
0x1fff08f0 D timerHead
0x1fff08f8 D hciPTMenabled
0x1fff08f9 D ctrlToHostEnable
0x1fff08fa D numHostBufs
0x1fff08fc D hciCtrlCmdToken
0x1fff0900 D bleEvtMask
0x1fff0904 D pHciEvtMask
0x1fff090c D hciTaskID
0x1fff090d D hciTestTaskID
0x1fff090e D hciGapTaskID
0x1fff090f D hciL2capTaskID
0x1fff0910 D hciSmpTaskID
0x1fff0911 D hciExtTaskID
0x1fff0914 D g_maxConnNum
0x1fff0915 D g_maxPktPerEventTx
0x1fff0916 D g_maxPktPerEventRx
0x1fff0917 D g_blePktVersion
0x1fff0918 D g_llRlEnable
0x1fff0919 D g_llScanMode
0x1fff091a D g_llAdvMode
0x1fff091b D LL_TaskID
0x1fff091c D llState
0x1fff091d D numComplPkts
0x1fff091e D numComplPktsLimit
0x1fff091f D fastTxRespTime
0x1fff0920 D g_llWlDeviceNum
0x1fff0921 D g_llRlDeviceNum
0x1fff0922 D rxFifoFlowCtrl
0x1fff0923 D llSecondaryState
0x1fff0924 D g_extAdvNumber
0x1fff0925 D g_perioAdvNumber
0x1fff0926 D g_schExtAdvNum
0x1fff0927 D g_currentExtAdv
0x1fff0928 D g_schExtAdvNum_periodic
0x1fff0929 D g_currentExtAdv_periodic
0x1fff092a D g_llPrdAdvDeviceNum
0x1fff092c D g_llRlTimeout
0x1fff0930 D g_advSetMaximumLen
0x1fff0934 D conn_param
0x1fff0938 D g_pExtendedAdvInfo
0x1fff093c D g_pPeriodicAdvInfo
0x1fff0940 D g_pLLcteISample
0x1fff0944 D g_pLLcteQSample
0x1fff0948 D g_llHdcDirAdvTime
0x1fff094c D g_pAdvSchInfo
0x1fff0950 D g_advPerSlotTick
0x1fff0954 D g_advSlotPeriodic
0x1fff0958 D g_pAdvSchInfo_periodic
0x1fff095c D g_timerExpiryTick
0x1fff0960 D chanMapUpdate
0x1fff0965 D ownPublicAddr
0x1fff096b D ownRandomAddr
0x1fff0972 D verInfo
0x1fff0978 D peerInfo
0x1fff0980 D g_currentAdvTimer
0x1fff0984 D g_currentTimerTask
0x1fff0988 D g_adv_taskID
0x1fff0989 D g_conn_taskID
0x1fff098a D g_smartWindowRTOCnt
0x1fff098b D isPeerRpaStore
0x1fff098c D g_same_rf_channel_flag
0x1fff098d D g_currentPeerAddrType
0x1fff098e D g_currentLocalAddrType
0x1fff098f D storeRpaListIndex
0x1fff0990 D llTaskState
0x1fff0992 D g_adv_taskEvent
0x1fff0994 D g_conn_taskEvent
0x1fff0998 D llWaitingIrq
0x1fff099c D ISR_entry_time
0x1fff09a0 D slave_conn_event_recv_delay
0x1fff09a4 D g_smartWindowLater
0x1fff09a8 D g_smartWindowActiveCnt
0x1fff09ac D g_smartWindowPreAnchPoint
0x1fff09b0 D g_getPn23_cnt
0x1fff09b4 D g_getPn23_seed
0x1fff09b8 D llScanTime
0x1fff09bc D p_perStatsByChan
0x1fff09c0 D LL_PLUS_AdvDataFilterCBack
0x1fff09c4 D LL_PLUS_ScanRequestFilterCBack
0x1fff09c8 D g_new_master_delta
0x1fff09cc D llScanT1
0x1fff09d0 D llCurrentScanChn
0x1fff09d4 D g_currentLocalRpa
0x1fff09da D g_currentPeerRpa
0x1fff09e0 D currentPeerRpa
0x1fff09e6 D g_dle_taskID
0x1fff09e8 D g_dle_taskEvent
0x1fff09ea D g_phyChg_taskID
0x1fff09ec D g_phyChg_taskEvent
0x1fff09f0 D g_smartWindowSize
0x1fff09f4 D g_smartWindowSizeNew
0x1fff09f8 D g_smartWindowActive
0x1fff09fc D g_interAuxPduDuration
0x1fff0a00 D g_rfTxPathCompensation
0x1fff0a02 D g_rfRxPathCompensation
0x1fff0a04 D connUpdateTimer
0x1fff0a0c D sleep_flag
0x1fff0a10 D g_wakeup_rtc_tick
0x1fff0a14 D g_counter_traking_avg
0x1fff0a18 D g_TIM2_IRQ_TIM3_CurrCount
0x1fff0a1c D g_TIM2_IRQ_to_Sleep_DeltTick
0x1fff0a20 D g_TIM2_IRQ_PendingTick
0x1fff0a24 D g_osal_tick_trim
0x1fff0a28 D g_osalTickTrim_mod
0x1fff0a2c D rtc_mod_value
0x1fff0a30 D g_counter_traking_cnt
0x1fff0a34 D sleep_tick 
0x1fff0a38 D counter_tracking
0x1fff0a3c D forever_write
0x1fff0a40 D g_TIM2_wakeup_delay
0x1fff0a44 D g_rfPhyTpCal0
0x1fff0a45 D g_rfPhyTpCal1
0x1fff0a46 D g_rfPhyTpCal0_2Mbps
0x1fff0a47 D g_rfPhyTpCal1_2Mbps
0x1fff0a48 D g_rfPhyTxPower
0x1fff0a49 D g_rfPhyPktFmt
0x1fff0a4a D g_system_clk
0x1fff0a4b D g_rfPhyClkSel
0x1fff0a4c D g_rxAdcClkSel
0x1fff0a4d D g_dtmModeType
0x1fff0a4e D g_dtmLength
0x1fff0a4f D g_dtmExtLen
0x1fff0a50 D g_dtmPKT
0x1fff0a51 D g_dtmTxPower
0x1fff0a52 D g_dtmRssi
0x1fff0a53 D g_dtmCarrSens
0x1fff0a54 D g_dtmPktIntv
0x1fff0a56 D g_dtmPktCount
0x1fff0a58 D g_dtmRxCrcNum
0x1fff0a5a D g_dtmRxTONum
0x1fff0a5c D g_dtmRsp
0x1fff0a5e D g_dtmFoff
0x1fff0a60 D g_rfPhyRxDcIQ
0x1fff0a64 D g_dtmTick
0x1fff0a68 D g_rfPhyFreqOffSet
0x1fff0a69 D g_rfPhyDtmCmd
0x1fff0a6b D g_rfPhyDtmEvt
0x1fff0a6d D g_dtmCmd
0x1fff0a6e D g_dtmFreq
0x1fff0a6f D g_dtmCtrl
0x1fff0a70 D g_dtmPara
0x1fff0a71 D g_dtmEvt
0x1fff0a72 D g_dtmStatus
0x1fff0a73 D g_dtmTpCalEnable
0x1fff0a74 D g_dtmPerAutoIntv
0x1fff0a78 D g_dtmAccessCode
0x1fff0a80 D g_system_reset_cause 
0x1fff0afc D cbTimers
0x1fff0b74 D g_llSleepContext
0x1fff0b84 D syncInfo
0x1fff0b96 D scanSyncInfo
0x1fff0ba6 D adv_param
0x1fff0bbc D scanInfo
0x1fff0bd4 D initInfo
0x1fff0be8 D extScanInfo
0x1fff0c10 D extInitInfo
0x1fff0c50 D g_llPeriodAdvSyncInfo
0x1fff0d30 D g_ll_conn_ctx
0x1fff0e48 D deviceFeatureSet
0x1fff0e51 D g_llWhitelist
0x1fff0e89 D g_llResolvinglist
0x1fff0ffc D g_pmCounters
0x1fff1084 D g_llPduLen
0x1fff10a0 D g_llPeriodicAdvlist
0x1fff10e0 D rfCounters
0x1fff10ec D ext_adv_hdr
0x1fff1118 D dataPkt
0x1fff1138 D cachedTRNGdata
0x1fff1144 D whiten_seed
0x1fff116c D g_tx_adv_buf
0x1fff1278 D g_tx_ext_adv_buf
0x1fff1384 D tx_scanRsp_desc
0x1fff1490 D g_rx_adv_buf

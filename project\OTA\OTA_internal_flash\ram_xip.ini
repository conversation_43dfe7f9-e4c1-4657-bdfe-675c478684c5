
FUNC void EraseChip(void) {
    unsigned long position;
    unsigned long wait;
    unsigned long i;
    
    i = 6;
    do
    {
        wait = 0x100;
        _WDWORD(0x4000c890, 0x06000001);
        while(wait--);
        wait = 0x100000;
        _WDWORD(0x4000c894, i*0x1000+0x1000);
        _WDWORD(0x4000c890, 0x200a0001);
        i--;
        while(wait--);
    }while(i!=0);
    
    wait = 0x100;
    _WDWORD(0x4000c890, 0x06000001);
    while(wait--);
    wait = 0x300000;
    _WDWORD(0x4000c894, 0x8000);
    _WDWORD(0x4000c890, 0x520a0001);
    while(wait--);
     
    i = 7;
    do
    {
        wait = 0x100;
        _WDWORD(0x4000c890, 0x06000001);
        while(wait--);
        wait = 0x300000;
        _WDWORD(0x4000c894, i*0x10000);
        _WDWORD(0x4000c890, 0xd80a0001);
        i--;
        while(wait--);
    }while(i!=0);
    
}

FUNC void Setup(void) {
    _WDWORD(0x4000f0a8, 0x00000000);
//    SP = _RDWORD(0x1fff2000);
//    PC = _RDWORD(0x1fff2004);
    
    SP = _RDWORD(0x0);
    PC = _RDWORD(0x4);

//	_WDWORD(0x4000f0cc, 0x1fff2001);	
}
EraseChip();
LOAD .\Objects\ota.axf NOCODE
LOAD .\Objects\ota.hexf
Setup();

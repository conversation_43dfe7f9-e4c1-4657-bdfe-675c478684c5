; *************************************************************
; *** Scatter-Loading Description File generated by uVision ***
; *************************************************************

;*********************************************************************************
; ota rom size limit 48KB
; sram 24kB
; xip 24kB
;*********************************************************************************
;20kB for ota_sram_code
 LR_ROM_CODE 0x1fff1838 0x009AC8 {  ; load region size_region
  ER_ROM_CODE 0x1fff1838 0x009AC8  {  ; load address = execution address
   *.o (RESET, +First)
   *(InRoot$$Sections)
   *.o (_section_sram_code_)
   *.o(.rev16_text,.revsh_text,.text)
   .ANY (+RO) 
   .ANY (+RW +ZI) 
	 .ANY (large_heap_buffer_area)
  }
 
}
;28kB for ota_xip_code
LR_ROM_XIP  0x1100b000 0x06000 {
  ER_ROM_XIP 0x1100b000 0x06000  {  ; load address = execution address
   ;devinfoservice.o(+RO)
   gatt*.o(+RO)
   gattservapp.o(+RO)
   l2cap*.o(+RO)
   att*.o(+RO)
   linkdb.o(+RO)
   sm*.o (+RO)
   gap*.o (+RO)
   peripheral.o(+RO)
   gpio.o(+RO)
;   ota_service.o(+RO)
;   ota_protocol.o(+RO)
   *.o(_section_xip_code_, _func_xip_code_.*)
  }
 } 

;***********************************************************************
; 2KB for Jump Table and Global Config
;***********************************************************************
LR_ROM_JT_GC  0x1fff0000 0x00800 {
  JUMP_TABLE 0x1fff0000 0x00400  {
   .ANY (jump_table_mem_area) 
  }
  GOLBAL_CONFIG 0x1fff0400 0x00400  {
   .ANY (global_config_area) 
  } 
}  

;***********************************************************************
; 16kB for ota_partition_buffer_area (statement : 16KB+16B)
;***********************************************************************
 
LR_OTA_SECTOR  0x1fffb300 0x4100 {
  OTA_SECTOR 0x1fffb300 0x4100  {
   .ANY (ota_partition_buffer_area) 	
  }
} 

;***********************************************************************
; 3kB for STACK and ota_app_loader 
;***********************************************************************
LR_OTA_LOADER  0x1ffff400 0xC00 {
  OTA_APP_LOADER 0x1ffff400 0xC00  {
   .ANY (ota_app_loader_area) 
   phy_sec_ext.o(*.bss)
   ;crc16.o
   clock.o(i.hal_system_soft_reset)
   aes.o
   .ANY (+RO) 
   .ANY (+RW +ZI) 
   .ANY(STACK)
  }
} 

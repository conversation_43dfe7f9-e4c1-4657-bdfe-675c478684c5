; *************************************************************
; *** Scatter-Loading Description File generated by uVision ***
; *************************************************************

 LR_IROM1 0x1fff1838 0x000E7C8 {  ; load region size_region
  ER_IROM1 0x1fff1838 0x000E7C8  {  ; load address = execution address
   *.o (RESET, +First)
   *(InRoot$$Sections)
  *.o(_section_sram_code_)
   .ANY (+RO)  
   .ANY (+RW +ZI)
  }
  
 }
 
LR_ROM_JT_GC  0x1fff0000 0x00800 {
  JUMP_TABLE 0x1fff0000 0x00400  {
   .ANY (jump_table_mem_area) 
	
  }
  GOLBAL_CONFIG 0x1fff0400 0x00400  {
   .ANY (global_config_area) 
	
  }  
 } 

LR_ROM_XIP  0x11020000 0x020000 {
  ER_ROM_XIP 0x11020000 0x020000  {  ; load address = execution address
   ;libethermind_mesh_models.o(+RO) 
   ;libethermind_utils.o(+RO) 
   ;libethermind_mesh_core.o(+RO) 
   ;appl_*.o(+RO)
   ;;;;;
   ;devinfoservice.o(+RO)
   gatt*.o(+RO)
   gattservapp.o(+RO)
   l2cap*.o(+RO)
   att*.o(+RO)
   linkdb.o(+RO)
   sm*.o(+RO)
   gap*.o(+RO)
   ;singlebank_otadongle.o(+RO)
   ;central.o(+RO)
   ;observer.o(+RO)
   *.o(_section_xip_code_, _func_xip_code_.*)
  }
 }  




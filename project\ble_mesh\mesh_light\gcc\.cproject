<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<?fileVersion 4.0.0?><cproject storage_type_id="org.eclipse.cdt.core.XmlProjectDescriptionStorage">
	<storageModule moduleId="org.eclipse.cdt.core.settings">
		<cconfiguration id="0.**********">
			<storageModule buildSystemId="org.eclipse.cdt.managedbuilder.core.configurationDataProvider" id="0.**********" moduleId="org.eclipse.cdt.core.settings" name="Default">
				<externalSettings/>
				<extensions>
					<extension id="org.eclipse.cdt.core.GASErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GmakeErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GLDErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.VCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.CWDLocator" point="org.eclipse.cdt.core.ErrorParser"/>
					<extension id="org.eclipse.cdt.core.GCCErrorParser" point="org.eclipse.cdt.core.ErrorParser"/>
				</extensions>
			</storageModule>
			<storageModule moduleId="cdtBuildSystem" version="4.0.0">
				<configuration buildProperties="" description="" id="0.**********" name="Default" parent="org.eclipse.cdt.build.core.prefbase.cfg">
					<folderInfo id="0.**********." name="/" resourcePath="">
						<toolChain id="org.eclipse.cdt.build.core.prefbase.toolchain.1486063884" name="No ToolChain" resourceTypeBasedDiscovery="false" superClass="org.eclipse.cdt.build.core.prefbase.toolchain">
							<targetPlatform id="org.eclipse.cdt.build.core.prefbase.toolchain.1486063884.91846552" name=""/>
							<builder id="org.eclipse.cdt.build.core.settings.default.builder.1395338293" keepEnvironmentInBuildfile="false" managedBuildOn="false" name="Gnu Make Builder" superClass="org.eclipse.cdt.build.core.settings.default.builder"/>
							<tool id="org.eclipse.cdt.build.core.settings.holder.libs.96294477" name="holder for library settings" superClass="org.eclipse.cdt.build.core.settings.holder.libs"/>
							<tool id="org.eclipse.cdt.build.core.settings.holder.839098753" name="Assembly" superClass="org.eclipse.cdt.build.core.settings.holder">
								<inputType id="org.eclipse.cdt.build.core.settings.holder.inType.716119453" languageId="org.eclipse.cdt.core.assembly" languageName="Assembly" sourceContentType="org.eclipse.cdt.core.asmSource" superClass="org.eclipse.cdt.build.core.settings.holder.inType"/>
							</tool>
							<tool id="org.eclipse.cdt.build.core.settings.holder.1951606793" name="GNU C++" superClass="org.eclipse.cdt.build.core.settings.holder">
								<inputType id="org.eclipse.cdt.build.core.settings.holder.inType.1132826742" languageId="org.eclipse.cdt.core.g++" languageName="GNU C++" sourceContentType="org.eclipse.cdt.core.cxxSource,org.eclipse.cdt.core.cxxHeader" superClass="org.eclipse.cdt.build.core.settings.holder.inType"/>
							</tool>
							<tool id="org.eclipse.cdt.build.core.settings.holder.378682402" name="GNU C" superClass="org.eclipse.cdt.build.core.settings.holder">
								<inputType id="org.eclipse.cdt.build.core.settings.holder.inType.274622255" languageId="org.eclipse.cdt.core.gcc" languageName="GNU C" sourceContentType="org.eclipse.cdt.core.cSource,org.eclipse.cdt.core.cHeader" superClass="org.eclipse.cdt.build.core.settings.holder.inType"/>
							</tool>
						</toolChain>
					</folderInfo>
				</configuration>
			</storageModule>
			<storageModule moduleId="org.eclipse.cdt.core.externalSettings"/>
		</cconfiguration>
	</storageModule>
	<storageModule moduleId="cdtBuildSystem" version="4.0.0">
		<project id="mesh_gcc.null.972753690" name="mesh_gcc"/>
	</storageModule>
	<storageModule moduleId="scannerConfiguration">
		<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		<scannerConfigBuildInfo instanceId="0.**********">
			<autodiscovery enabled="true" problemReportingEnabled="true" selectedProfileId=""/>
		</scannerConfigBuildInfo>
	</storageModule>
	<storageModule moduleId="org.eclipse.cdt.core.LanguageSettingsProviders"/>
</cproject>
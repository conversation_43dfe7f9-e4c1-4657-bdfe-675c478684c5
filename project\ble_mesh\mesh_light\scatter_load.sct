; *************************************************************
; *** Scatter-Loading Description File generated by uVision ***
; *************************************************************

LR_IROM1 0x1fff1838 0xE7C8 {  ; load region size_region
 ER_IROM1 0x1fff1838 0xE7C8  {  ; load address = execution address
  *.o (RESET, +First)
  *(InRoot$$Sections)
  *.o(_section_sram_code_)
  .ANY (+RO)  
  .ANY (+RW +ZI)
 }
}
LR_ROM_JT_GC  0x1fff0000 0x00800 {
  JUMP_TABLE 0x1fff0000 0x00400  {
   .ANY (jump_table_mem_area) 
	
  }
  GOLBAL_CONFIG 0x1fff0400 0x00400  {
   .ANY (global_config_area) 
	
  }  
 } 
LR_ROM_XIP  0x1101b000 0x01e000 {
  ER_ROM_XIP 0x1101b000 0x01e000  {  ; load address = execution address	
	libethermind_mesh_models.lib (+RO) 
	libethermind_utils.lib (+RO) 
	libethermind_mesh_core.lib (+RO)
	appl_sample_example_phylight.o(+RO)
	devinfoservice.o(+RO)
	gatt*.o(+RO)
	gattservapp.o(+RO)
	l2cap*.o(+RO)
	att*.o(+RO)
	linkdb.o(+RO)
	sm*.o(+RO)
	gap*.o(+RO)

	; 实际存在的模块 - 移动到XIP以节省RAM
	main.o(+RO)
	led_light.o(+RO)           ; LED控制
	pwm.o(+RO)                 ; PWM驱动
	uart.o(+RO)                ; UART驱动
	em_timer.o(+RO)            ; 定时器
	cli_model.o(+RO)           ; CLI模型
	model_state_handler_pl.o(+RO)  ; 模型状态处理
	blebrr_pl.o(+RO)           ; BLE承载层平台
	blebrr.o(+RO)              ; BLE承载层
	blebrr_gatt.o(+RO)         ; BLE GATT承载
	blemesh_main.o(+RO)        ; Mesh主程序
	appl_sample_example_phylight.o(+RO)  ; 应用示例
	blemesh.o(+RO)             ; Mesh核心
	cliface.o(+RO)             ; CLI接口
	my_printf.o(+RO)           ; 打印函数
	em_debug.o(+RO)            ; 调试模块
	prov_pl.o(+RO)             ; 配置平台层
	access_ps.o(+RO)           ; 访问持久化存储
	ms_limit_config.o(+RO)     ; Mesh限制配置
	printfa.o(+RO)             ; 打印格式化
	flash.o(+RO)              	; Flash操作
	gpio.o(+RO)               	; GPIO驱动
	pwrmgr.o(+RO)              ; 电源管理
	self_uart_func.o(+RO)     	; 自定义UART功能

	; 可选移动的模块（如果RAM仍然不够）

	;gapgattserver.o(+RO)       ; GAP GATT服务器
	;crc16.o(+RO)               ; CRC16计算
	;clock.o(+RO)               ; 时钟驱动
	;em_os.o(+RO)               ; OS抽象层
	;cry.o(+RO)                 ; 加密模块
	;aes-ccm.o(+RO)             ; AES-CCM加密
	;aes.o(+RO)                 ; AES加密
	;mesh_services.o(+RO)       ; Mesh服务	
	;vendormodel_server.o(+RO)
	;devinfoservice.o(+RO)     ; 设备信息服务
	;gattservapp.o(+RO)        ; GATT服务应用
	;em_platform.o(+RO)        ; 平台抽象层

   *.o(_section_xip_code_, _func_xip_code_.*)
  }
 }  




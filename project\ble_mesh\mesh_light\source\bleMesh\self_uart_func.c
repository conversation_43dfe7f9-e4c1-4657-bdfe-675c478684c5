/*
 * @Author: li
 * @Date: 2025-07-02 14:56:51
 * @LastEditTime: 2025-07-04 15:43:42
 * @Description:
 */
#include "uart.h"
#include "self_uart_func.h"
// #include "circular_buf.h"
#include "string.h"

// 发送数据缓存区
#define UART_TX_BUF_SIZE 256
#define UART_RX_BUF_SIZE 512
// 校验模型
#define NONE_DATA_CHECK (0 << 1)
#define DLT645_07_CHECK (1 << 1)
#define _376_1_CHECK (2 << 1)
// 队列总长度
#define QUEUE_SIZE 512
// 队列最多分多少条进行数据接收，一般使用的645大概只有二十多个字节，16*20=320<=512
#define QUEUE_PENDING_SIZE 16

struct uart1_cfg_param
{
    bool  is_tt;                                // transparent transmission 透传模式
    uint8 check_mode;                           // 数据校验模式
    uint8 pending_len;                          // 待处理数据总的字节数目，下面的索引加队列分成了每次收到的数据
    uint8 pending_idx;                          // 待处理数据索引
    uint8 pending_queue[QUEUE_PENDING_SIZE];    // 待处理数据单条长度
    // 测试
    uint8 *pending_data;    // 待处理数据
};

static uint8_t uart_tx_buf[2][UART_TX_BUF_SIZE] = {0x00};
static uint8_t uart_rx_buf[UART_RX_BUF_SIZE]    = {0x00};

// 获取基类的指针,不能直接取
#define UART_GENERAL (*((struct uart_func **)self))
#define UART_INDEX (UART_GENERAL->index)

static int user_uart_write(struct uart_func *self, uint8 *data, uint16 len)
{
    return hal_uart_send_buff(UART_INDEX, data, len);
}

static int user_uart_init(struct uart_func *self, bool flag)
{
    uart_Cfg_t cfg = {
        .tx_pin      = P31,
        .rx_pin      = P32,
        .rts_pin     = GPIO_DUMMY,
        .cts_pin     = GPIO_DUMMY,
        .baudrate    = 115200,
        .use_fifo    = TRUE,
        .hw_fwctrl   = FALSE,
        .use_tx_buf  = flag,
        .parity      = FALSE,
        .evt_handler = NULL,
    };

    hal_uart_init(cfg, UART_INDEX);

    if(flag) { UART_GENERAL->set_irq_tx_buf(self); }

    return 0;
}

static int user_uart_deinit(struct uart_func *self)
{
    return hal_uart_deinit(UART_INDEX);
}

static int user_set_irq_tx_buf(struct uart_func *self)
{
    return hal_uart_set_tx_buf(UART_INDEX, uart_tx_buf[UART_INDEX], UART_TX_BUF_SIZE);
}

// 串口0相关功能
static struct uart_func uart0_general_func = {.index          = UART0,
                                              .init           = user_uart_init,
                                              .write          = user_uart_write,
                                              .deinit         = user_uart_deinit,
                                              .set_irq_tx_buf = user_set_irq_tx_buf};

static struct uart0_func uart0_dev = {.uart = &uart0_general_func};
struct uart0_func       *p_uart0   = &uart0_dev;

// 串口1相关功能
static struct uart1_cfg_param uart1_cfg = {
    .is_tt         = TRUE,
    .check_mode    = NONE_DATA_CHECK,
    .pending_len   = 0,
    .pending_idx   = 0,
    .pending_queue = {0x00},
    .pending_data  = uart_rx_buf,
};

static int user_uart1_set_tt(struct uart1_func *self, bool flag)
{
    self->cfg->is_tt = flag;
    return 0;
}

static bool user_uart1_get_tt(struct uart1_func *self)
{
    return self->cfg->is_tt;
}

static int user_uart1_set_check(struct uart1_func *self, uint8 mode)
{
    self->cfg->check_mode = mode;
    return 0;
}

static uint8 user_uart1_get_check(struct uart1_func *self)
{
    return self->cfg->check_mode;
}

static int user_uart1_get_pending_len(struct uart1_func *self)
{
    return self->cfg->pending_len;
}

static uint8 user_uart1_get_pending_idx(struct uart1_func *self)
{
    return self->cfg->pending_idx;
}

static int user_uart1_read_pending(struct uart1_func *self, uint8 *data, uint32 len)
{
    memcpy(data, self->cfg->pending_data, len);
    self->cfg->pending_len -= len;
    return 0;
}

static int user_uart1_recv_to_pending(struct uart1_func *self, uint8 *data, uint32 len)
{
    memcpy(self->cfg->pending_data, data, len);
    self->cfg->pending_len += len;
    return 0;
}

static struct uart_func uart1_general_func = {.index          = UART1,
                                              .init           = user_uart_init,
                                              .write          = user_uart_write,
                                              .deinit         = user_uart_deinit,
                                              .set_irq_tx_buf = user_set_irq_tx_buf};

static struct uart1_func uart1_dev = {.uart            = &uart1_general_func,
                                      .cfg             = &uart1_cfg,
                                      .set_tt          = user_uart1_set_tt,
                                      .get_tt          = user_uart1_get_tt,
                                      .set_check       = user_uart1_set_check,
                                      .get_check       = user_uart1_get_check,
                                      .get_pending_len = user_uart1_get_pending_len,
                                      .get_pending_idx = user_uart1_get_pending_idx,
                                      .read_pending    = user_uart1_read_pending,
                                      .recv_to_pending = user_uart1_recv_to_pending};

struct uart1_func *p_uart1 = &uart1_dev;

#ifndef __SELF_UART_FUNC_H__
#define __SELF_UART_FUNC_H__

#include "uart.h"

/**
 * @description: 串口基础功能
 */
struct uart_func
{
    UART_INDEX_e index;
    // 初始化
    int (*init)(struct uart_func *self, bool flag);
    // 发送
    int (*write)(struct uart_func *self, uint8 *data, uint16 len);
    // 释放
    int (*deinit)(struct uart_func *self);
    // 设置中断发送数组
    int (*set_irq_tx_buf)(struct uart_func *self);
};

/**
 * @description: 串口0功能，只需要基础的就行,别的代码已经实现
 */
struct uart0_func
{
    struct uart_func *uart;    // 必须放在第1个
};

/**
 * @description: 串口1功能，全功能
 * 返回值int是0表示成功
 * 其余的返回值是数据
 */
struct uart1_func
{
    struct uart_func       *uart;    // 必须放在第1个
    // 参数指针，私有
    struct uart1_cfg_param *cfg;
    // 从串口数据到待处理
    int (*recv_to_pending)(struct uart1_func *self, uint8 *data, uint32 len);
    // 读取数据，自动清除待处理信息
    int (*read_pending)(struct uart1_func *self, uint8 *data, uint32 len);
    // 设置透传模式
    int (*set_tt)(struct uart1_func *self, bool flag);
    // 获取透传模式
    bool (*get_tt)(struct uart1_func *self);
    // 设置数据校验模式
    int (*set_check)(struct uart1_func *self, uint8 mode);
    // 获取当前数据校验模式
    uint8 (*get_check)(struct uart1_func *self);
    // 获取当前待处理数据总字节，读取后不清除
    int (*get_pending_len)(struct uart1_func *self);
    // 获取当前多少条待处理数据，不为0表示有数据，使用read去读
    uint8 (*get_pending_idx)(struct uart1_func *self);
};

extern struct uart0_func *p_uart0;
extern struct uart1_func *p_uart1;

#endif
